'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var umi = require('@metaplex-foundation/umi');
var plugin = require('./plugin.cjs');

function createUmi(endpointOrConnection, rpcOptions) {
  return umi.createUmi().use(typeof endpointOrConnection === 'string' ? plugin.defaultPlugins(endpointOrConnection, rpcOptions) : plugin.defaultPlugins(endpointOrConnection, rpcOptions));
}

exports.defaultPlugins = plugin.defaultPlugins;
exports.createUmi = createUmi;
//# sourceMappingURL=index.cjs.map
