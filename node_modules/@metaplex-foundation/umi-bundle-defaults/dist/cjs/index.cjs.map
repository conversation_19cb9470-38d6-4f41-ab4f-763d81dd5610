{"version": 3, "file": "index.cjs", "sources": ["../../src/index.ts"], "sourcesContent": ["import { Umi, createUmi as baseCreateUmi } from '@metaplex-foundation/umi';\nimport type { ChunkGetAccountsRpcOptions } from '@metaplex-foundation/umi-rpc-chunk-get-accounts';\nimport type { Web3JsRpcOptions } from '@metaplex-foundation/umi-rpc-web3js';\nimport type { Connection as Web3JsConnection } from '@solana/web3.js';\nimport { defaultPlugins } from './plugin';\n\nexport function createUmi(\n  endpoint: string,\n  rpcOptions?: Web3JsRpcOptions & ChunkGetAccountsRpcOptions\n): Umi;\nexport function createUmi(\n  connection: Web3JsConnection,\n  rpcOptions?: ChunkGetAccountsRpcOptions\n): Umi;\nexport function createUmi(\n  endpointOrConnection: string | Web3JsConnection,\n  rpcOptions?: Web3JsRpcOptions & ChunkGetAccountsRpcOptions\n): Umi {\n  return baseCreateUmi().use(\n    typeof endpointOrConnection === 'string'\n      ? defaultPlugins(endpointOrConnection, rpcOptions)\n      : defaultPlugins(endpointOrConnection, rpcOptions)\n  );\n}\n\nexport * from './plugin';\n"], "names": ["createUmi", "endpointOrConnection", "rpcOptions", "baseCreateUmi", "use", "defaultPlugins"], "mappings": ";;;;;;;AAcO,SAASA,SAAS,CACvBC,oBAA+C,EAC/CC,UAA0D,EACrD;EACL,OAAOC,aAAa,EAAE,CAACC,GAAG,CACxB,OAAOH,oBAAoB,KAAK,QAAQ,GACpCI,qBAAc,CAACJ,oBAAoB,EAAEC,UAAU,CAAC,GAChDG,qBAAc,CAACJ,oBAAoB,EAAEC,UAAU,CAAC,CACrD,CAAA;AACH;;;;;"}