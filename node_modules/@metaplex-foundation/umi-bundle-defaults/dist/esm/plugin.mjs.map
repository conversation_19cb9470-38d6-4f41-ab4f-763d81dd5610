{"version": 3, "file": "plugin.mjs", "sources": ["../../src/plugin.ts"], "sourcesContent": ["import type { UmiPlugin } from '@metaplex-foundation/umi';\nimport { httpDownloader } from '@metaplex-foundation/umi-downloader-http';\nimport { web3JsEddsa } from '@metaplex-foundation/umi-eddsa-web3js';\nimport { fetchHttp } from '@metaplex-foundation/umi-http-fetch';\nimport { defaultProgramRepository } from '@metaplex-foundation/umi-program-repository';\nimport {\n  web3JsRpc,\n  Web3JsRpcOptions,\n} from '@metaplex-foundation/umi-rpc-web3js';\nimport {\n  chunkGetAccountsRpc,\n  ChunkGetAccountsRpcOptions,\n} from '@metaplex-foundation/umi-rpc-chunk-get-accounts';\nimport { dataViewSerializer } from '@metaplex-foundation/umi-serializer-data-view';\nimport { web3JsTransactionFactory } from '@metaplex-foundation/umi-transaction-factory-web3js';\nimport type { Connection as Web3JsConnection } from '@solana/web3.js';\n\nexport function defaultPlugins(\n  endpoint: string,\n  rpcOptions?: Web3JsRpcOptions & ChunkGetAccountsRpcOptions\n): UmiPlugin;\nexport function defaultPlugins(\n  connection: Web3JsConnection,\n  rpcOptions?: ChunkGetAccountsRpcOptions\n): UmiPlugin;\nexport function defaultPlugins(\n  endpointOrConnection: string | Web3JsConnection,\n  rpcOptions?: Web3JsRpcOptions & ChunkGetAccountsRpcOptions\n): UmiPlugin {\n  return {\n    install(umi) {\n      umi.use(dataViewSerializer());\n      umi.use(defaultProgramRepository());\n      umi.use(fetchHttp());\n      umi.use(httpDownloader());\n      umi.use(web3JsEddsa());\n      umi.use(\n        typeof endpointOrConnection === 'string'\n          ? web3JsRpc(endpointOrConnection, rpcOptions)\n          : web3JsRpc(endpointOrConnection)\n      );\n      umi.use(chunkGetAccountsRpc(rpcOptions?.getAccountsChunkSize));\n      umi.use(web3JsTransactionFactory());\n    },\n  };\n}\n"], "names": ["defaultPlugins", "endpointOrConnection", "rpcOptions", "install", "umi", "use", "dataViewSerializer", "defaultProgramRepository", "fetchHttp", "httpDownloader", "web3JsEddsa", "web3JsRpc", "chunkGetAccountsRpc", "getAccountsChunkSize", "web3JsTransactionFactory"], "mappings": ";;;;;;;;;AAyBO,SAASA,cAAc,CAC5BC,oBAA+C,EAC/CC,UAA0D,EAC/C;EACX,OAAO;IACLC,OAAO,CAACC,GAAG,EAAE;AACXA,MAAAA,GAAG,CAACC,GAAG,CAACC,kBAAkB,EAAE,CAAC,CAAA;AAC7BF,MAAAA,GAAG,CAACC,GAAG,CAACE,wBAAwB,EAAE,CAAC,CAAA;AACnCH,MAAAA,GAAG,CAACC,GAAG,CAACG,SAAS,EAAE,CAAC,CAAA;AACpBJ,MAAAA,GAAG,CAACC,GAAG,CAACI,cAAc,EAAE,CAAC,CAAA;AACzBL,MAAAA,GAAG,CAACC,GAAG,CAACK,WAAW,EAAE,CAAC,CAAA;AACtBN,MAAAA,GAAG,CAACC,GAAG,CACL,OAAOJ,oBAAoB,KAAK,QAAQ,GACpCU,SAAS,CAACV,oBAAoB,EAAEC,UAAU,CAAC,GAC3CS,SAAS,CAACV,oBAAoB,CAAC,CACpC,CAAA;MACDG,GAAG,CAACC,GAAG,CAACO,mBAAmB,CAACV,UAAU,EAAEW,oBAAoB,CAAC,CAAC,CAAA;AAC9DT,MAAAA,GAAG,CAACC,GAAG,CAACS,wBAAwB,EAAE,CAAC,CAAA;AACrC,KAAA;GACD,CAAA;AACH;;;;"}