{"version": 3, "file": "createHttpDownloader.cjs", "sources": ["../../src/createHttpDownloader.ts"], "sourcesContent": ["import {\n  Context,\n  createGenericFile,\n  DownloaderInterface,\n  DownloaderOptions,\n  GenericFile,\n  request,\n} from '@metaplex-foundation/umi';\n\nexport function createHttpDownloader(\n  context: Pick<Context, 'http'>\n): DownloaderInterface {\n  const downloadOne = async (\n    uri: string,\n    options: DownloaderOptions = {}\n  ): Promise<GenericFile> => {\n    const response = await context.http.send(\n      request().get(uri).withAbortSignal(options.signal)\n    );\n    return createGenericFile(response.body, uri);\n  };\n\n  const download = async (\n    uris: string[],\n    options: DownloaderOptions = {}\n  ): Promise<GenericFile[]> =>\n    Promise.all(uris.map((uri) => downloadOne(uri, options)));\n\n  const downloadJson = async <T>(\n    uri: string,\n    options: DownloaderOptions = {}\n  ): Promise<T> => {\n    const response = await context.http.send<T>(\n      request().get(uri).withAbortSignal(options.signal)\n    );\n    return response.data;\n  };\n\n  return { download, downloadJson };\n}\n"], "names": ["createHttpDownloader", "context", "downloadOne", "uri", "options", "response", "http", "send", "request", "get", "withAbortSignal", "signal", "createGenericFile", "body", "download", "uris", "Promise", "all", "map", "downloadJson", "data"], "mappings": ";;;;;;AASO,SAASA,oBAAoB,CAClCC,OAA8B,EACT;EACrB,MAAMC,WAAW,GAAG,OAClBC,GAAW,EACXC,OAA0B,GAAG,EAAE,KACN;IACzB,MAAMC,QAAQ,GAAG,MAAMJ,OAAO,CAACK,IAAI,CAACC,IAAI,CACtCC,WAAO,EAAE,CAACC,GAAG,CAACN,GAAG,CAAC,CAACO,eAAe,CAACN,OAAO,CAACO,MAAM,CAAC,CACnD,CAAA;AACD,IAAA,OAAOC,qBAAiB,CAACP,QAAQ,CAACQ,IAAI,EAAEV,GAAG,CAAC,CAAA;GAC7C,CAAA;EAED,MAAMW,QAAQ,GAAG,OACfC,IAAc,EACdX,OAA0B,GAAG,EAAE,KAE/BY,OAAO,CAACC,GAAG,CAACF,IAAI,CAACG,GAAG,CAAEf,GAAG,IAAKD,WAAW,CAACC,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC,CAAA;EAE3D,MAAMe,YAAY,GAAG,OACnBhB,GAAW,EACXC,OAA0B,GAAG,EAAE,KAChB;IACf,MAAMC,QAAQ,GAAG,MAAMJ,OAAO,CAACK,IAAI,CAACC,IAAI,CACtCC,WAAO,EAAE,CAACC,GAAG,CAACN,GAAG,CAAC,CAACO,eAAe,CAACN,OAAO,CAACO,MAAM,CAAC,CACnD,CAAA;IACD,OAAON,QAAQ,CAACe,IAAI,CAAA;GACrB,CAAA;EAED,OAAO;IAAEN,QAAQ;AAAEK,IAAAA,YAAAA;GAAc,CAAA;AACnC;;;;"}