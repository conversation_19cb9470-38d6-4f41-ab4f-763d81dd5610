{"version": 3, "file": "plugin.mjs", "sources": ["../../src/plugin.ts"], "sourcesContent": ["import { UmiPlugin } from '@metaplex-foundation/umi';\nimport { createHttpDownloader } from './createHttpDownloader';\n\nexport const httpDownloader = (): UmiPlugin => ({\n  install(umi) {\n    umi.downloader = createHttpDownloader(umi);\n  },\n});\n"], "names": ["httpDownloader", "install", "umi", "downloader", "createHttpDownloader"], "mappings": ";;AAGO,MAAMA,cAAc,GAAG,OAAkB;EAC9CC,OAAO,CAACC,GAAG,EAAE;AACXA,IAAAA,GAAG,CAACC,UAAU,GAAGC,oBAAoB,CAACF,GAAG,CAAC,CAAA;AAC5C,GAAA;AACF,CAAC;;;;"}