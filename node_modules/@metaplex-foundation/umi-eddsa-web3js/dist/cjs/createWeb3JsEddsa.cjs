'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var umi = require('@metaplex-foundation/umi');
var umiWeb3jsAdapters = require('@metaplex-foundation/umi-web3js-adapters');
var ed25519 = require('@noble/curves/ed25519');
var web3_js = require('@solana/web3.js');

function createWeb3JsEddsa() {
  const generateKeypair = () => umiWeb3jsAdapters.fromWeb3JsKeypair(web3_js.Keypair.generate());
  const createKeypairFromSecretKey = secretKey => umiWeb3jsAdapters.fromWeb3JsKeypair(web3_js.Keypair.fromSecretKey(secretKey));
  const createKeypairFromSeed = seed => umiWeb3jsAdapters.fromWeb3JsKeypair(web3_js.Keypair.fromSeed(seed));
  const isOnCurve = input => web3_js.PublicKey.isOnCurve(umiWeb3jsAdapters.toWeb3JsPublicKey(umi.publicKey(input)));
  const findPda = (programId, seeds) => {
    const [key, bump] = web3_js.PublicKey.findProgramAddressSync(seeds, umiWeb3jsAdapters.toWeb3JsPublicKey(umi.publicKey(programId)));
    return [umiWeb3jsAdapters.fromWeb3JsPublicKey(key), bump];
  };
  const sign = (message, keypair) => ed25519.ed25519.sign(message, keypair.secretKey.slice(0, 32));
  const verify = (message, signature, publicKey) => ed25519.ed25519.verify(signature, message, umi.publicKeyBytes(publicKey));
  return {
    generateKeypair,
    createKeypairFromSecretKey,
    createKeypairFromSeed,
    isOnCurve,
    findPda,
    sign,
    verify
  };
}

exports.createWeb3JsEddsa = createWeb3JsEddsa;
//# sourceMappingURL=createWeb3JsEddsa.cjs.map
