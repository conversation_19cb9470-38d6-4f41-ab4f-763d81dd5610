{"version": 3, "file": "plugin.cjs", "sources": ["../../src/plugin.ts"], "sourcesContent": ["import { UmiPlugin } from '@metaplex-foundation/umi';\nimport { createWeb3JsEddsa } from './createWeb3JsEddsa';\n\nexport const web3JsEddsa = (): UmiPlugin => ({\n  install(umi) {\n    umi.eddsa = createWeb3JsEddsa();\n  },\n});\n"], "names": ["web3JsEddsa", "install", "umi", "eddsa", "createWeb3JsEddsa"], "mappings": ";;;;;;AAGO,MAAMA,WAAW,GAAG,OAAkB;EAC3CC,OAAO,CAACC,GAAG,EAAE;AACXA,IAAAA,GAAG,CAACC,KAAK,GAAGC,mCAAiB,EAAE,CAAA;AACjC,GAAA;AACF,CAAC;;;;"}