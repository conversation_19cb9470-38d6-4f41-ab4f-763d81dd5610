import { publicKey, publicKeyBytes } from '@metaplex-foundation/umi';
import { fromWeb3JsKeypair, toWeb3JsPublicKey, fromWeb3JsPublicKey } from '@metaplex-foundation/umi-web3js-adapters';
import { ed25519 } from '@noble/curves/ed25519';
import { Keypair, PublicKey } from '@solana/web3.js';

function createWeb3JsEddsa() {
  const generateKeypair = () => fromWeb3JsKeypair(Keypair.generate());
  const createKeypairFromSecretKey = secretKey => fromWeb3JsKeypair(Keypair.fromSecretKey(secretKey));
  const createKeypairFromSeed = seed => fromWeb3JsKeypair(Keypair.fromSeed(seed));
  const isOnCurve = input => PublicKey.isOnCurve(toWeb3JsPublicKey(publicKey(input)));
  const findPda = (programId, seeds) => {
    const [key, bump] = PublicKey.findProgramAddressSync(seeds, toWeb3JsPublicKey(publicKey(programId)));
    return [fromWeb3JsPublicKey(key), bump];
  };
  const sign = (message, keypair) => ed25519.sign(message, keypair.secretKey.slice(0, 32));
  const verify = (message, signature, publicKey) => ed25519.verify(signature, message, publicKeyBytes(publicKey));
  return {
    generateKeypair,
    createKeypairFromSecretKey,
    createKeypairFromSeed,
    isOnCurve,
    findPda,
    sign,
    verify
  };
}

export { createWeb3JsEddsa };
//# sourceMappingURL=createWeb3JsEddsa.mjs.map
