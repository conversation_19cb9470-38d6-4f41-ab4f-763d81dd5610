{"version": 3, "file": "createWeb3JsEddsa.mjs", "sources": ["../../src/createWeb3JsEddsa.ts"], "sourcesContent": ["import {\n  EddsaInterface,\n  Keypair,\n  Pda,\n  publicKey,\n  PublicKey,\n  publicKeyBytes,\n  PublicKeyInput,\n} from '@metaplex-foundation/umi';\nimport {\n  fromWeb3JsKeypair,\n  fromWeb3JsPublicKey,\n  toWeb3JsPublicKey,\n} from '@metaplex-foundation/umi-web3js-adapters';\nimport { ed25519 } from '@noble/curves/ed25519';\nimport {\n  Keypair as Web3JsKeypair,\n  PublicKey as Web3JsPublicKey,\n} from '@solana/web3.js';\n\nexport function createWeb3JsEddsa(): EddsaInterface {\n  const generateKeypair = (): Keypair =>\n    fromWeb3JsKeypair(Web3JsKeypair.generate());\n\n  const createKeypairFromSecretKey = (secretKey: Uint8Array): Keypair =>\n    fromWeb3JsKeypair(Web3JsKeypair.fromSecretKey(secretKey));\n\n  const createKeypairFromSeed = (seed: Uint8Array): Keypair =>\n    fromWeb3JsKeypair(Web3JsKeypair.fromSeed(seed));\n\n  const isOnCurve = (input: PublicKeyInput): boolean =>\n    Web3JsPublicKey.isOnCurve(toWeb3JsPublicKey(publicKey(input)));\n\n  const findPda = (programId: PublicKeyInput, seeds: Uint8Array[]): Pda => {\n    const [key, bump] = Web3JsPublicKey.findProgramAddressSync(\n      seeds,\n      toWeb3JsPublicKey(publicKey(programId))\n    );\n    return [fromWeb3JsPublicKey(key), bump] as Pda;\n  };\n\n  const sign = (message: Uint8Array, keypair: Keypair): Uint8Array =>\n    ed25519.sign(message, keypair.secretKey.slice(0, 32));\n\n  const verify = (\n    message: Uint8Array,\n    signature: Uint8Array,\n    publicKey: PublicKey\n  ): boolean => ed25519.verify(signature, message, publicKeyBytes(publicKey));\n\n  return {\n    generateKeypair,\n    createKeypairFromSecretKey,\n    createKeypairFromSeed,\n    isOnCurve,\n    findPda,\n    sign,\n    verify,\n  };\n}\n"], "names": ["createWeb3JsEddsa", "generateKeypair", "fromWeb3JsKeypair", "Web3JsKeypair", "generate", "createKeypairFromSecretKey", "secret<PERSON>ey", "fromSecretKey", "createKeypairFromSeed", "seed", "fromSeed", "isOnCurve", "input", "Web3JsPublicKey", "toWeb3JsPublicKey", "public<PERSON>ey", "findPda", "programId", "seeds", "key", "bump", "findProgramAddressSync", "fromWeb3JsPublicKey", "sign", "message", "keypair", "ed25519", "slice", "verify", "signature", "publicKeyBytes"], "mappings": ";;;;;AAoBO,SAASA,iBAAiB,GAAmB;EAClD,MAAMC,eAAe,GAAG,MACtBC,iBAAiB,CAACC,OAAa,CAACC,QAAQ,EAAE,CAAC,CAAA;AAE7C,EAAA,MAAMC,0BAA0B,GAAIC,SAAqB,IACvDJ,iBAAiB,CAACC,OAAa,CAACI,aAAa,CAACD,SAAS,CAAC,CAAC,CAAA;AAE3D,EAAA,MAAME,qBAAqB,GAAIC,IAAgB,IAC7CP,iBAAiB,CAACC,OAAa,CAACO,QAAQ,CAACD,IAAI,CAAC,CAAC,CAAA;AAEjD,EAAA,MAAME,SAAS,GAAIC,KAAqB,IACtCC,SAAe,CAACF,SAAS,CAACG,iBAAiB,CAACC,SAAS,CAACH,KAAK,CAAC,CAAC,CAAC,CAAA;AAEhE,EAAA,MAAMI,OAAO,GAAG,CAACC,SAAyB,EAAEC,KAAmB,KAAU;AACvE,IAAA,MAAM,CAACC,GAAG,EAAEC,IAAI,CAAC,GAAGP,SAAe,CAACQ,sBAAsB,CACxDH,KAAK,EACLJ,iBAAiB,CAACC,SAAS,CAACE,SAAS,CAAC,CAAC,CACxC,CAAA;AACD,IAAA,OAAO,CAACK,mBAAmB,CAACH,GAAG,CAAC,EAAEC,IAAI,CAAC,CAAA;GACxC,CAAA;EAED,MAAMG,IAAI,GAAG,CAACC,OAAmB,EAAEC,OAAgB,KACjDC,OAAO,CAACH,IAAI,CAACC,OAAO,EAAEC,OAAO,CAACnB,SAAS,CAACqB,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAA;EAEvD,MAAMC,MAAM,GAAG,CACbJ,OAAmB,EACnBK,SAAqB,EACrBd,SAAoB,KACRW,OAAO,CAACE,MAAM,CAACC,SAAS,EAAEL,OAAO,EAAEM,cAAc,CAACf,SAAS,CAAC,CAAC,CAAA;EAE3E,OAAO;IACLd,eAAe;IACfI,0BAA0B;IAC1BG,qBAAqB;IACrBG,SAAS;IACTK,OAAO;IACPO,IAAI;AACJK,IAAAA,MAAAA;GACD,CAAA;AACH;;;;"}