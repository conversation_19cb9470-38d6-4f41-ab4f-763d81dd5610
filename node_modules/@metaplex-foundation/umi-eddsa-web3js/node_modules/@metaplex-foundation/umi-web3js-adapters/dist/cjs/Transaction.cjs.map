{"version": 3, "file": "Transaction.cjs", "sources": ["../../src/Transaction.ts"], "sourcesContent": ["import { base58, Transaction } from '@metaplex-foundation/umi';\nimport {\n  SIGNATURE_LENGTH_IN_BYTES,\n  Transaction as Web3JsLegacyTransaction,\n  Message as Web3JsMessage,\n  VersionedTransaction as Web3JsTransaction,\n} from '@solana/web3.js';\nimport { fromWeb3JsMessage, toWeb3JsMessage } from './TransactionMessage';\n\nexport function fromWeb3JsTransaction(\n  web3JsTransaction: Web3JsTransaction\n): Transaction {\n  return {\n    message: fromWeb3JsMessage(web3JsTransaction.message),\n    serializedMessage: web3JsTransaction.message.serialize(),\n    signatures: web3JsTransaction.signatures,\n  };\n}\n\nexport function toWeb3JsTransaction(\n  transaction: Transaction\n): Web3JsTransaction {\n  return new Web3JsTransaction(\n    toWeb3JsMessage(transaction.message),\n    transaction.signatures\n  );\n}\n\nexport function fromWeb3JsLegacyTransaction(\n  web3JsLegacyTransaction: Web3JsLegacyTransaction\n): Transaction {\n  const web3JsMessage = web3JsLegacyTransaction.compileMessage();\n  const web3JsLegacySignatures = web3JsLegacyTransaction.signatures.reduce(\n    (all, one) => {\n      all[one.publicKey.toBase58()] = one.signature\n        ? new Uint8Array(one.signature)\n        : null;\n      return all;\n    },\n    {} as Record<string, Uint8Array | null>\n  );\n\n  const signatures = [];\n  for (let i = 0; i < web3JsMessage.header.numRequiredSignatures; i += 1) {\n    const pubkey = web3JsMessage.accountKeys[i].toBase58();\n    const signature = web3JsLegacySignatures[pubkey] ?? null;\n    signatures.push(signature ?? new Uint8Array(SIGNATURE_LENGTH_IN_BYTES));\n  }\n\n  return {\n    message: fromWeb3JsMessage(web3JsMessage),\n    serializedMessage: web3JsMessage.serialize(),\n    signatures,\n  };\n}\n\nexport function toWeb3JsLegacyTransaction(\n  transaction: Transaction\n): Web3JsLegacyTransaction {\n  const web3JsTransaction = toWeb3JsTransaction({\n    ...transaction,\n    message: { ...transaction.message, version: 'legacy' },\n  });\n  return Web3JsLegacyTransaction.populate(\n    web3JsTransaction.message as Web3JsMessage,\n    web3JsTransaction.signatures.map(\n      (signature) => base58.deserialize(signature)[0]\n    )\n  );\n}\n"], "names": ["fromWeb3JsTransaction", "web3JsTransaction", "message", "fromWeb3JsMessage", "serializedMessage", "serialize", "signatures", "toWeb3JsTransaction", "transaction", "Web3JsTransaction", "toWeb3JsMessage", "fromWeb3JsLegacyTransaction", "web3JsLegacyTransaction", "web3JsMessage", "compileMessage", "web3JsLegacySignatures", "reduce", "all", "one", "public<PERSON>ey", "toBase58", "signature", "Uint8Array", "i", "header", "numRequiredSignatures", "pubkey", "accountKeys", "push", "SIGNATURE_LENGTH_IN_BYTES", "toWeb3JsLegacyTransaction", "version", "Web3JsLegacyTransaction", "populate", "map", "base58", "deserialize"], "mappings": ";;;;;;;;AASO,SAASA,qBAAqB,CACnCC,iBAAoC,EACvB;EACb,OAAO;AACLC,IAAAA,OAAO,EAAEC,oCAAiB,CAACF,iBAAiB,CAACC,OAAO,CAAC;AACrDE,IAAAA,iBAAiB,EAAEH,iBAAiB,CAACC,OAAO,CAACG,SAAS,EAAE;IACxDC,UAAU,EAAEL,iBAAiB,CAACK,UAAAA;GAC/B,CAAA;AACH,CAAA;AAEO,SAASC,mBAAmB,CACjCC,WAAwB,EACL;AACnB,EAAA,OAAO,IAAIC,4BAAiB,CAC1BC,kCAAe,CAACF,WAAW,CAACN,OAAO,CAAC,EACpCM,WAAW,CAACF,UAAU,CACvB,CAAA;AACH,CAAA;AAEO,SAASK,2BAA2B,CACzCC,uBAAgD,EACnC;AACb,EAAA,MAAMC,aAAa,GAAGD,uBAAuB,CAACE,cAAc,EAAE,CAAA;AAC9D,EAAA,MAAMC,sBAAsB,GAAGH,uBAAuB,CAACN,UAAU,CAACU,MAAM,CACtE,CAACC,GAAG,EAAEC,GAAG,KAAK;IACZD,GAAG,CAACC,GAAG,CAACC,SAAS,CAACC,QAAQ,EAAE,CAAC,GAAGF,GAAG,CAACG,SAAS,GACzC,IAAIC,UAAU,CAACJ,GAAG,CAACG,SAAS,CAAC,GAC7B,IAAI,CAAA;AACR,IAAA,OAAOJ,GAAG,CAAA;GACX,EACD,EAAE,CACH,CAAA;EAED,MAAMX,UAAU,GAAG,EAAE,CAAA;AACrB,EAAA,KAAK,IAAIiB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGV,aAAa,CAACW,MAAM,CAACC,qBAAqB,EAAEF,CAAC,IAAI,CAAC,EAAE;IACtE,MAAMG,MAAM,GAAGb,aAAa,CAACc,WAAW,CAACJ,CAAC,CAAC,CAACH,QAAQ,EAAE,CAAA;AACtD,IAAA,MAAMC,SAAS,GAAGN,sBAAsB,CAACW,MAAM,CAAC,IAAI,IAAI,CAAA;IACxDpB,UAAU,CAACsB,IAAI,CAACP,SAAS,IAAI,IAAIC,UAAU,CAACO,iCAAyB,CAAC,CAAC,CAAA;AACzE,GAAA;EAEA,OAAO;AACL3B,IAAAA,OAAO,EAAEC,oCAAiB,CAACU,aAAa,CAAC;AACzCT,IAAAA,iBAAiB,EAAES,aAAa,CAACR,SAAS,EAAE;AAC5CC,IAAAA,UAAAA;GACD,CAAA;AACH,CAAA;AAEO,SAASwB,yBAAyB,CACvCtB,WAAwB,EACC;EACzB,MAAMP,iBAAiB,GAAGM,mBAAmB,CAAC;AAC5C,IAAA,GAAGC,WAAW;AACdN,IAAAA,OAAO,EAAE;MAAE,GAAGM,WAAW,CAACN,OAAO;AAAE6B,MAAAA,OAAO,EAAE,QAAA;AAAS,KAAA;AACvD,GAAC,CAAC,CAAA;EACF,OAAOC,mBAAuB,CAACC,QAAQ,CACrChC,iBAAiB,CAACC,OAAO,EACzBD,iBAAiB,CAACK,UAAU,CAAC4B,GAAG,CAC7Bb,SAAS,IAAKc,UAAM,CAACC,WAAW,CAACf,SAAS,CAAC,CAAC,CAAC,CAAC,CAChD,CACF,CAAA;AACH;;;;;;;"}