{"version": 3, "file": "TransactionMessage.cjs", "sources": ["../../src/TransactionMessage.ts"], "sourcesContent": ["import {\n  base58,\n  TransactionInput,\n  TransactionMessage,\n} from '@metaplex-foundation/umi';\nimport {\n  AddressLookupTableAccount as Web3JsAddressLookupTableAccount,\n  Message as Web3JsMessageLegacy,\n  MessageV0 as Web3JsMessageV0,\n} from '@solana/web3.js';\nimport { toWeb3JsInstruction } from './Instruction';\nimport { fromWeb3JsPublicKey, toWeb3JsPublicKey } from './PublicKey';\n\nexport function fromWeb3JsMessage(\n  message: Web3JsMessageLegacy | Web3JsMessageV0\n): TransactionMessage {\n  return {\n    version: message.version,\n    header: message.header,\n    accounts: message.staticAccountKeys.map(fromWeb3JsPublicKey),\n    blockhash: message.recentBlockhash,\n    instructions: message.compiledInstructions.map((instruction) => ({\n      programIndex: instruction.programIdIndex,\n      accountIndexes: instruction.accountKeyIndexes,\n      data: new Uint8Array(instruction.data),\n    })),\n    addressLookupTables: message.addressTableLookups.map((lookup) => ({\n      publicKey: fromWeb3JsPublicKey(lookup.accountKey),\n      writableIndexes: lookup.writableIndexes,\n      readonlyIndexes: lookup.readonlyIndexes,\n    })),\n  };\n}\n\nexport function toWeb3JsMessage(\n  message: TransactionMessage\n): Web3JsMessageLegacy | Web3JsMessageV0 {\n  if (message.version === 'legacy') {\n    return new Web3JsMessageLegacy({\n      header: message.header,\n      accountKeys: message.accounts.map(toWeb3JsPublicKey),\n      recentBlockhash: message.blockhash,\n      instructions: message.instructions.map((instruction) => ({\n        programIdIndex: instruction.programIndex,\n        accounts: instruction.accountIndexes,\n        data: base58.deserialize(instruction.data)[0],\n      })),\n    });\n  }\n\n  return new Web3JsMessageV0({\n    header: message.header,\n    staticAccountKeys: message.accounts.map(toWeb3JsPublicKey),\n    recentBlockhash: message.blockhash,\n    compiledInstructions: message.instructions.map((instruction) => ({\n      programIdIndex: instruction.programIndex,\n      accountKeyIndexes: instruction.accountIndexes,\n      data: instruction.data,\n    })),\n    addressTableLookups: message.addressLookupTables.map((lookup) => ({\n      accountKey: toWeb3JsPublicKey(lookup.publicKey),\n      writableIndexes: lookup.writableIndexes,\n      readonlyIndexes: lookup.readonlyIndexes,\n    })),\n  });\n}\n\nexport function toWeb3JsMessageFromInput(\n  input: TransactionInput\n): Web3JsMessageLegacy | Web3JsMessageV0 {\n  if (input.version === 'legacy') {\n    return Web3JsMessageLegacy.compile({\n      payerKey: toWeb3JsPublicKey(input.payer),\n      instructions: input.instructions.map(toWeb3JsInstruction),\n      recentBlockhash: input.blockhash,\n    });\n  }\n\n  return Web3JsMessageV0.compile({\n    payerKey: toWeb3JsPublicKey(input.payer),\n    instructions: input.instructions.map(toWeb3JsInstruction),\n    recentBlockhash: input.blockhash,\n    addressLookupTableAccounts: input.addressLookupTables?.map(\n      (account) =>\n        new Web3JsAddressLookupTableAccount({\n          key: toWeb3JsPublicKey(account.publicKey),\n          state: {\n            addresses: account.addresses.map(toWeb3JsPublicKey),\n            authority: undefined,\n            deactivationSlot: BigInt(`0x${'ff'.repeat(8)}`),\n            lastExtendedSlot: 0,\n            lastExtendedSlotStartIndex: 0,\n          },\n        })\n    ),\n  });\n}\n"], "names": ["fromWeb3JsMessage", "message", "version", "header", "accounts", "staticAccount<PERSON><PERSON>s", "map", "fromWeb3JsPublicKey", "blockhash", "recent<PERSON><PERSON><PERSON><PERSON>", "instructions", "compiledInstructions", "instruction", "programIndex", "programIdIndex", "accountIndexes", "accountKeyIndexes", "data", "Uint8Array", "addressLookupTables", "addressTableLookups", "lookup", "public<PERSON>ey", "accountKey", "writableIndexes", "readonlyIndexes", "toWeb3JsMessage", "Web3JsMessageLegacy", "accountKeys", "toWeb3JsPublicKey", "base58", "deserialize", "Web3JsMessageV0", "toWeb3JsMessageFromInput", "input", "compile", "payerKey", "payer", "toWeb3JsInstruction", "addressLookupTableAccounts", "account", "Web3JsAddressLookupTableAccount", "key", "state", "addresses", "authority", "undefined", "deactivationSlot", "BigInt", "repeat", "lastExtendedSlot", "lastExtendedSlotStartIndex"], "mappings": ";;;;;;;;;AAaO,SAASA,iBAAiB,CAC/BC,OAA8C,EAC1B;EACpB,OAAO;IACLC,OAAO,EAAED,OAAO,CAACC,OAAO;IACxBC,MAAM,EAAEF,OAAO,CAACE,MAAM;IACtBC,QAAQ,EAAEH,OAAO,CAACI,iBAAiB,CAACC,GAAG,CAACC,6BAAmB,CAAC;IAC5DC,SAAS,EAAEP,OAAO,CAACQ,eAAe;IAClCC,YAAY,EAAET,OAAO,CAACU,oBAAoB,CAACL,GAAG,CAAEM,WAAW,KAAM;MAC/DC,YAAY,EAAED,WAAW,CAACE,cAAc;MACxCC,cAAc,EAAEH,WAAW,CAACI,iBAAiB;AAC7CC,MAAAA,IAAI,EAAE,IAAIC,UAAU,CAACN,WAAW,CAACK,IAAI,CAAA;AACvC,KAAC,CAAC,CAAC;IACHE,mBAAmB,EAAElB,OAAO,CAACmB,mBAAmB,CAACd,GAAG,CAAEe,MAAM,KAAM;AAChEC,MAAAA,SAAS,EAAEf,6BAAmB,CAACc,MAAM,CAACE,UAAU,CAAC;MACjDC,eAAe,EAAEH,MAAM,CAACG,eAAe;MACvCC,eAAe,EAAEJ,MAAM,CAACI,eAAAA;AAC1B,KAAC,CAAC,CAAA;GACH,CAAA;AACH,CAAA;AAEO,SAASC,eAAe,CAC7BzB,OAA2B,EACY;AACvC,EAAA,IAAIA,OAAO,CAACC,OAAO,KAAK,QAAQ,EAAE;IAChC,OAAO,IAAIyB,eAAmB,CAAC;MAC7BxB,MAAM,EAAEF,OAAO,CAACE,MAAM;MACtByB,WAAW,EAAE3B,OAAO,CAACG,QAAQ,CAACE,GAAG,CAACuB,2BAAiB,CAAC;MACpDpB,eAAe,EAAER,OAAO,CAACO,SAAS;MAClCE,YAAY,EAAET,OAAO,CAACS,YAAY,CAACJ,GAAG,CAAEM,WAAW,KAAM;QACvDE,cAAc,EAAEF,WAAW,CAACC,YAAY;QACxCT,QAAQ,EAAEQ,WAAW,CAACG,cAAc;QACpCE,IAAI,EAAEa,UAAM,CAACC,WAAW,CAACnB,WAAW,CAACK,IAAI,CAAC,CAAC,CAAC,CAAA;AAC9C,OAAC,CAAC,CAAA;AACJ,KAAC,CAAC,CAAA;AACJ,GAAA;EAEA,OAAO,IAAIe,iBAAe,CAAC;IACzB7B,MAAM,EAAEF,OAAO,CAACE,MAAM;IACtBE,iBAAiB,EAAEJ,OAAO,CAACG,QAAQ,CAACE,GAAG,CAACuB,2BAAiB,CAAC;IAC1DpB,eAAe,EAAER,OAAO,CAACO,SAAS;IAClCG,oBAAoB,EAAEV,OAAO,CAACS,YAAY,CAACJ,GAAG,CAAEM,WAAW,KAAM;MAC/DE,cAAc,EAAEF,WAAW,CAACC,YAAY;MACxCG,iBAAiB,EAAEJ,WAAW,CAACG,cAAc;MAC7CE,IAAI,EAAEL,WAAW,CAACK,IAAAA;AACpB,KAAC,CAAC,CAAC;IACHG,mBAAmB,EAAEnB,OAAO,CAACkB,mBAAmB,CAACb,GAAG,CAAEe,MAAM,KAAM;AAChEE,MAAAA,UAAU,EAAEM,2BAAiB,CAACR,MAAM,CAACC,SAAS,CAAC;MAC/CE,eAAe,EAAEH,MAAM,CAACG,eAAe;MACvCC,eAAe,EAAEJ,MAAM,CAACI,eAAAA;AAC1B,KAAC,CAAC,CAAA;AACJ,GAAC,CAAC,CAAA;AACJ,CAAA;AAEO,SAASQ,wBAAwB,CACtCC,KAAuB,EACgB;AACvC,EAAA,IAAIA,KAAK,CAAChC,OAAO,KAAK,QAAQ,EAAE;IAC9B,OAAOyB,eAAmB,CAACQ,OAAO,CAAC;AACjCC,MAAAA,QAAQ,EAAEP,2BAAiB,CAACK,KAAK,CAACG,KAAK,CAAC;MACxC3B,YAAY,EAAEwB,KAAK,CAACxB,YAAY,CAACJ,GAAG,CAACgC,+BAAmB,CAAC;MACzD7B,eAAe,EAAEyB,KAAK,CAAC1B,SAAAA;AACzB,KAAC,CAAC,CAAA;AACJ,GAAA;EAEA,OAAOwB,iBAAe,CAACG,OAAO,CAAC;AAC7BC,IAAAA,QAAQ,EAAEP,2BAAiB,CAACK,KAAK,CAACG,KAAK,CAAC;IACxC3B,YAAY,EAAEwB,KAAK,CAACxB,YAAY,CAACJ,GAAG,CAACgC,+BAAmB,CAAC;IACzD7B,eAAe,EAAEyB,KAAK,CAAC1B,SAAS;IAChC+B,0BAA0B,EAAEL,KAAK,CAACf,mBAAmB,EAAEb,GAAG,CACvDkC,OAAO,IACN,IAAIC,iCAA+B,CAAC;AAClCC,MAAAA,GAAG,EAAEb,2BAAiB,CAACW,OAAO,CAAClB,SAAS,CAAC;AACzCqB,MAAAA,KAAK,EAAE;QACLC,SAAS,EAAEJ,OAAO,CAACI,SAAS,CAACtC,GAAG,CAACuB,2BAAiB,CAAC;AACnDgB,QAAAA,SAAS,EAAEC,SAAS;QACpBC,gBAAgB,EAAEC,MAAM,CAAE,CAAI,EAAA,EAAA,IAAI,CAACC,MAAM,CAAC,CAAC,CAAE,CAAA,CAAC,CAAC;AAC/CC,QAAAA,gBAAgB,EAAE,CAAC;AACnBC,QAAAA,0BAA0B,EAAE,CAAA;AAC9B,OAAA;AACF,KAAC,CAAC,CAAA;AAER,GAAC,CAAC,CAAA;AACJ;;;;;;"}