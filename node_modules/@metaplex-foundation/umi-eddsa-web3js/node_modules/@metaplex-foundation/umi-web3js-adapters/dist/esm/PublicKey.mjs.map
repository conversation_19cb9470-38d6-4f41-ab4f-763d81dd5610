{"version": 3, "file": "PublicKey.mjs", "sources": ["../../src/PublicKey.ts"], "sourcesContent": ["import { PublicKey } from '@metaplex-foundation/umi';\nimport { PublicKey as Web3JsPublicKey } from '@solana/web3.js';\n\nexport function fromWeb3JsPublicKey(publicKey: Web3JsPublicKey): PublicKey {\n  return publicKey.toBase58() as PublicKey;\n}\n\nexport function toWeb3JsPublicKey(publicKey: PublicKey): Web3JsPublicKey {\n  return new Web3JsPublicKey(publicKey);\n}\n"], "names": ["fromWeb3JsPublicKey", "public<PERSON>ey", "toBase58", "toWeb3JsPublicKey", "Web3JsPublicKey"], "mappings": ";;AAGO,SAASA,mBAAmB,CAACC,SAA0B,EAAa;EACzE,OAAOA,SAAS,CAACC,QAAQ,EAAE,CAAA;AAC7B,CAAA;AAEO,SAASC,iBAAiB,CAACF,SAAoB,EAAmB;AACvE,EAAA,OAAO,IAAIG,SAAe,CAACH,SAAS,CAAC,CAAA;AACvC;;;;"}