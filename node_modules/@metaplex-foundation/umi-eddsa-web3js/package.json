{"name": "@metaplex-foundation/umi-eddsa-web3js", "version": "1.1.1", "description": "An Eddsa implementation relying on Solana's web3.js", "license": "MIT", "sideEffects": false, "module": "dist/esm/index.mjs", "main": "dist/cjs/index.cjs", "types": "dist/types/index.d.ts", "exports": {".": {"types": "./dist/types/index.d.ts", "import": "./dist/esm/index.mjs", "require": "./dist/cjs/index.cjs"}}, "files": ["/dist/cjs", "/dist/esm", "/dist/types", "/src"], "dependencies": {"@metaplex-foundation/umi-web3js-adapters": "1.1.1", "@noble/curves": "^1.0.0", "yaml": "^2.7.0"}, "peerDependencies": {"@metaplex-foundation/umi": "1.1.1", "@solana/web3.js": "^1.72.0"}, "devDependencies": {"@ava/typescript": "^3.0.1", "@metaplex-foundation/umi": "1.1.1", "@solana/web3.js": "^1.72.0", "ava": "^5.1.0"}, "publishConfig": {"access": "public"}, "author": "Metaplex Maintainers <<EMAIL>>", "homepage": "https://metaplex.com", "repository": {"url": "https://github.com/metaplex-foundation/umi.git"}, "typedoc": {"entryPoint": "./src/index.ts", "readmeFile": "./README.md", "displayName": "umi-eddsa-web3js"}, "ava": {"typescript": {"compile": false, "rewritePaths": {"src/": "dist/test/src/", "test/": "dist/test/test/"}}}, "scripts": {"lint": "eslint --ext js,ts,tsx src", "lint:fix": "eslint --fix --ext js,ts,tsx src", "clean": "<PERSON><PERSON><PERSON> dist", "build": "pnpm clean && tsc && tsc -p test/tsconfig.json && rollup -c", "test": "ava"}}