{"version": 3, "file": "createFetchHttp.cjs", "sources": ["../../src/createFetchHttp.ts"], "sourcesContent": ["import {\n  HttpInterface,\n  HttpRequest,\n  HttpResponse,\n} from '@metaplex-foundation/umi';\nimport fetch, { BodyInit, RequestInit } from 'node-fetch';\n\nexport function createFetchHttp(): HttpInterface {\n  return {\n    send: async <ResponseData, RequestData = any>(\n      request: HttpRequest<RequestData>\n    ): Promise<HttpResponse<ResponseData>> => {\n      const headers = request.headers\n        ? Object.entries(request.headers).reduce(\n            (acc, [name, headers]) => ({\n              ...acc,\n              [name.toLowerCase()]: (Array.isArray(headers)\n                ? headers.join(', ')\n                : headers\n              ).toLowerCase(),\n            }),\n            {} as Record<string, string>\n          )\n        : {};\n\n      const isJsonRequest =\n        headers['content-type']?.includes('application/json') ?? false;\n\n      let body: BodyInit | undefined;\n      if (isJsonRequest && request.data) {\n        body = JSON.stringify(request.data);\n      } else {\n        body = request.data as BodyInit | undefined;\n      }\n\n      const requestInit: RequestInit = {\n        method: request.method,\n        body,\n        headers,\n        follow: request.maxRedirects,\n        signal: request.signal as any,\n        timeout: request.timeout,\n      };\n\n      const response = await fetch(request.url, requestInit);\n      const isJsonResponse =\n        response.headers.get('content-type')?.includes('application/json') ??\n        false;\n\n      const bodyAsText = await response.text();\n      const bodyAsJson = isJsonResponse ? JSON.parse(bodyAsText) : undefined;\n\n      return {\n        data: bodyAsJson ?? bodyAsText,\n        body: bodyAsText,\n        ok: response.ok,\n        status: response.status,\n        statusText: response.statusText,\n        headers: Object.fromEntries(response.headers.entries()),\n      };\n    },\n  };\n}\n"], "names": ["createFetchHttp", "send", "request", "headers", "Object", "entries", "reduce", "acc", "name", "toLowerCase", "Array", "isArray", "join", "isJsonRequest", "includes", "body", "data", "JSON", "stringify", "requestInit", "method", "follow", "maxRedirects", "signal", "timeout", "response", "fetch", "url", "isJsonResponse", "get", "bodyAsText", "text", "bodyAsJson", "parse", "undefined", "ok", "status", "statusText", "fromEntries"], "mappings": ";;;;;;;;;;AAOO,SAASA,eAAe,GAAkB;EAC/C,OAAO;IACLC,IAAI,EAAE,MACJC,OAAiC,IACO;MACxC,MAAMC,OAAO,GAAGD,OAAO,CAACC,OAAO,GAC3BC,MAAM,CAACC,OAAO,CAACH,OAAO,CAACC,OAAO,CAAC,CAACG,MAAM,CACpC,CAACC,GAAG,EAAE,CAACC,IAAI,EAAEL,OAAO,CAAC,MAAM;AACzB,QAAA,GAAGI,GAAG;QACN,CAACC,IAAI,CAACC,WAAW,EAAE,GAAG,CAACC,KAAK,CAACC,OAAO,CAACR,OAAO,CAAC,GACzCA,OAAO,CAACS,IAAI,CAAC,IAAI,CAAC,GAClBT,OAAO,EACTM,WAAW,EAAA;AACf,OAAC,CAAC,EACF,EAAE,CACH,GACD,EAAE,CAAA;AAEN,MAAA,MAAMI,aAAa,GACjBV,OAAO,CAAC,cAAc,CAAC,EAAEW,QAAQ,CAAC,kBAAkB,CAAC,IAAI,KAAK,CAAA;AAEhE,MAAA,IAAIC,IAA0B,CAAA;AAC9B,MAAA,IAAIF,aAAa,IAAIX,OAAO,CAACc,IAAI,EAAE;QACjCD,IAAI,GAAGE,IAAI,CAACC,SAAS,CAAChB,OAAO,CAACc,IAAI,CAAC,CAAA;AACrC,OAAC,MAAM;QACLD,IAAI,GAAGb,OAAO,CAACc,IAA4B,CAAA;AAC7C,OAAA;AAEA,MAAA,MAAMG,WAAwB,GAAG;QAC/BC,MAAM,EAAElB,OAAO,CAACkB,MAAM;QACtBL,IAAI;QACJZ,OAAO;QACPkB,MAAM,EAAEnB,OAAO,CAACoB,YAAY;QAC5BC,MAAM,EAAErB,OAAO,CAACqB,MAAa;QAC7BC,OAAO,EAAEtB,OAAO,CAACsB,OAAAA;OAClB,CAAA;MAED,MAAMC,QAAQ,GAAG,MAAMC,yBAAK,CAACxB,OAAO,CAACyB,GAAG,EAAER,WAAW,CAAC,CAAA;AACtD,MAAA,MAAMS,cAAc,GAClBH,QAAQ,CAACtB,OAAO,CAAC0B,GAAG,CAAC,cAAc,CAAC,EAAEf,QAAQ,CAAC,kBAAkB,CAAC,IAClE,KAAK,CAAA;AAEP,MAAA,MAAMgB,UAAU,GAAG,MAAML,QAAQ,CAACM,IAAI,EAAE,CAAA;MACxC,MAAMC,UAAU,GAAGJ,cAAc,GAAGX,IAAI,CAACgB,KAAK,CAACH,UAAU,CAAC,GAAGI,SAAS,CAAA;MAEtE,OAAO;QACLlB,IAAI,EAAEgB,UAAU,IAAIF,UAAU;AAC9Bf,QAAAA,IAAI,EAAEe,UAAU;QAChBK,EAAE,EAAEV,QAAQ,CAACU,EAAE;QACfC,MAAM,EAAEX,QAAQ,CAACW,MAAM;QACvBC,UAAU,EAAEZ,QAAQ,CAACY,UAAU;QAC/BlC,OAAO,EAAEC,MAAM,CAACkC,WAAW,CAACb,QAAQ,CAACtB,OAAO,CAACE,OAAO,EAAE,CAAA;OACvD,CAAA;AACH,KAAA;GACD,CAAA;AACH;;;;"}