{"version": 3, "file": "plugin.cjs", "sources": ["../../src/plugin.ts"], "sourcesContent": ["import { UmiPlugin } from '@metaplex-foundation/umi';\nimport { createFetchHttp } from './createFetchHttp';\n\nexport const fetchHttp = (): UmiPlugin => ({\n  install(umi) {\n    umi.http = createFetchHttp();\n  },\n});\n"], "names": ["fetchHttp", "install", "umi", "http", "createFetchHttp"], "mappings": ";;;;;;AAGO,MAAMA,SAAS,GAAG,OAAkB;EACzCC,OAAO,CAACC,GAAG,EAAE;AACXA,IAAAA,GAAG,CAACC,IAAI,GAAGC,+BAAe,EAAE,CAAA;AAC9B,GAAA;AACF,CAAC;;;;"}