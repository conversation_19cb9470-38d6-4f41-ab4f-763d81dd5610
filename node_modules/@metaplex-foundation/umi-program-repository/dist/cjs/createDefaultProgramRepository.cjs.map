{"version": 3, "file": "createDefaultProgramRepository.cjs", "sources": ["../../src/createDefaultProgramRepository.ts"], "sourcesContent": ["import {\n  <PERSON>luster,\n  ClusterFilter,\n  Context,\n  ErrorWithLogs,\n  isPublicKey,\n  Program,\n  ProgramError,\n  ProgramRepositoryInterface,\n  publicKey,\n  PublicKey,\n  PublicKeyInput,\n  Transaction,\n} from '@metaplex-foundation/umi';\nimport {\n  ProgramErrorNotRecognizedError,\n  ProgramNotRecognizedError,\n} from './errors';\n\nexport function createDefaultProgramRepository(\n  context: Pick<Context, 'rpc'>,\n  initialPrograms: Program[] = [],\n  initialBindings: Record<string, string | PublicKey> = {}\n): ProgramRepositoryInterface {\n  const programs: Program[] = [...initialPrograms];\n  const bindings: Record<string, string | PublicKey> = { ...initialBindings };\n\n  const has = (\n    identifier: string | PublicKey,\n    clusterFilter: ClusterFilter = 'current'\n  ): boolean => {\n    const programs = all(clusterFilter);\n    const resolvedIdentifier = resolveBinding(identifier);\n    return isPublicKey(resolvedIdentifier)\n      ? programs.some((p) => p.publicKey === resolvedIdentifier)\n      : programs.some((p) => p.name === resolvedIdentifier);\n  };\n\n  const get = <T extends Program = Program>(\n    identifier: string | PublicKey,\n    clusterFilter: ClusterFilter = 'current'\n  ): T => {\n    const cluster = resolveClusterFilter(clusterFilter);\n    const programs = all(clusterFilter);\n    const resolvedIdentifier = resolveBinding(identifier);\n    const program = isPublicKey(resolvedIdentifier)\n      ? programs.find((p) => p.publicKey === resolvedIdentifier)\n      : programs.find((p) => p.name === resolvedIdentifier);\n\n    if (!program) {\n      throw new ProgramNotRecognizedError(resolvedIdentifier, cluster);\n    }\n\n    return program as T;\n  };\n\n  const getPublicKey = (\n    identifier: string | PublicKey,\n    fallback?: PublicKeyInput,\n    clusterFilter?: ClusterFilter\n  ): PublicKey => {\n    try {\n      return get(identifier, clusterFilter).publicKey;\n    } catch (error) {\n      if (fallback === undefined) throw error;\n      return publicKey(fallback);\n    }\n  };\n\n  const all = (clusterFilter: ClusterFilter = 'current'): Program[] => {\n    const cluster = resolveClusterFilter(clusterFilter);\n    return cluster === '*'\n      ? programs\n      : programs.filter((program) => program.isOnCluster(cluster));\n  };\n\n  const add = (program: Program, overrides = true): void => {\n    if (!overrides && has(program.publicKey, '*')) return;\n    programs.unshift(program);\n  };\n\n  const bind = (abstract: string, concrete: string | PublicKey): void => {\n    bindings[abstract] = concrete;\n    resolveBinding(abstract); // Ensures the binding is valid.\n  };\n\n  const unbind = (abstract: string): void => {\n    delete bindings[abstract];\n  };\n\n  const clone = (): ProgramRepositoryInterface =>\n    createDefaultProgramRepository(context, programs, bindings);\n\n  const resolveError = (\n    error: ErrorWithLogs,\n    transaction: Transaction\n  ): ProgramError | null => {\n    // Ensure the error as logs.\n    if (!Array.isArray(error.logs) || error.logs.length === 0) return null;\n    const logs = error.logs.join('\\n');\n\n    // Parse the instruction number.\n    const instructionRegex = /Error processing Instruction (\\d+):/;\n    const instruction = error.message.match(instructionRegex)?.[1] ?? null;\n\n    // Parse the error code.\n    const errorCodeRegex = /Custom program error: (0x[a-f0-9]+)/i;\n    const errorCodeString = logs.match(errorCodeRegex)?.[1] ?? null;\n    const errorCode = errorCodeString ? parseInt(errorCodeString, 16) : null;\n\n    // Ensure we could find an instruction number and an error code.\n    if (instruction === null || errorCode === null) return null;\n\n    // Get the program ID from the instruction in the transaction.\n    const instructionNumber: number = parseInt(instruction, 10);\n    const programIndex: number | null =\n      transaction.message.instructions?.[instructionNumber]?.programIndex ??\n      null;\n    const programId = programIndex\n      ? transaction.message.accounts[programIndex]\n      : null;\n\n    // Ensure we were able to find a program ID for the instruction.\n    if (!programId) return null;\n\n    // Find a registered program if any.\n    let program: Program;\n    try {\n      program = get(programId);\n    } catch (_programNotFoundError) {\n      return null;\n    }\n\n    // Finally, resolve the error.\n    const resolvedError = program.getErrorFromCode(errorCode, error);\n    return resolvedError ?? new ProgramErrorNotRecognizedError(program, error);\n  };\n\n  const resolveClusterFilter = (clusterFilter: ClusterFilter): Cluster | '*' =>\n    clusterFilter === 'current' ? context.rpc.getCluster() : clusterFilter;\n\n  const resolveBinding = (\n    identifier: string | PublicKey,\n    stack: string[] = []\n  ): string | PublicKey => {\n    if (isPublicKey(identifier)) return identifier;\n    if (bindings[identifier] === undefined) return identifier;\n    const stackWithIdentifier = [...stack, identifier];\n    if (stack.includes(identifier)) {\n      throw new Error(\n        `Circular binding detected: ${stackWithIdentifier.join(' -> ')}`\n      );\n    }\n    return resolveBinding(bindings[identifier], stackWithIdentifier);\n  };\n\n  return {\n    has,\n    get,\n    getPublicKey,\n    all,\n    add,\n    bind,\n    unbind,\n    clone,\n    resolveError,\n  };\n}\n"], "names": ["createDefaultProgramRepository", "context", "initialPrograms", "initialBindings", "programs", "bindings", "has", "identifier", "clusterFilter", "all", "resolvedIdentifier", "resolveBinding", "isPublicKey", "some", "p", "public<PERSON>ey", "name", "get", "cluster", "resolveClusterFilter", "program", "find", "ProgramNotRecognizedError", "getPublicKey", "fallback", "error", "undefined", "filter", "isOnCluster", "add", "overrides", "unshift", "bind", "abstract", "concrete", "unbind", "clone", "resolveError", "transaction", "Array", "isArray", "logs", "length", "join", "instructionRegex", "instruction", "message", "match", "errorCodeRegex", "errorCodeString", "errorCode", "parseInt", "instructionNumber", "programIndex", "instructions", "programId", "accounts", "_programNotFoundError", "resolvedError", "getErrorFromCode", "ProgramErrorNotRecognizedError", "rpc", "getCluster", "stack", "stackWithIdentifier", "includes", "Error"], "mappings": ";;;;;;;AAmBO,SAASA,8BAA8B,CAC5CC,OAA6B,EAC7BC,eAA0B,GAAG,EAAE,EAC/BC,eAAmD,GAAG,EAAE,EAC5B;AAC5B,EAAA,MAAMC,QAAmB,GAAG,CAAC,GAAGF,eAAe,CAAC,CAAA;AAChD,EAAA,MAAMG,QAA4C,GAAG;IAAE,GAAGF,eAAAA;GAAiB,CAAA;EAE3E,MAAMG,GAAG,GAAG,CACVC,UAA8B,EAC9BC,aAA4B,GAAG,SAAS,KAC5B;AACZ,IAAA,MAAMJ,QAAQ,GAAGK,GAAG,CAACD,aAAa,CAAC,CAAA;AACnC,IAAA,MAAME,kBAAkB,GAAGC,cAAc,CAACJ,UAAU,CAAC,CAAA;AACrD,IAAA,OAAOK,eAAW,CAACF,kBAAkB,CAAC,GAClCN,QAAQ,CAACS,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACC,SAAS,KAAKL,kBAAkB,CAAC,GACxDN,QAAQ,CAACS,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACE,IAAI,KAAKN,kBAAkB,CAAC,CAAA;GACxD,CAAA;EAED,MAAMO,GAAG,GAAG,CACVV,UAA8B,EAC9BC,aAA4B,GAAG,SAAS,KAClC;AACN,IAAA,MAAMU,OAAO,GAAGC,oBAAoB,CAACX,aAAa,CAAC,CAAA;AACnD,IAAA,MAAMJ,QAAQ,GAAGK,GAAG,CAACD,aAAa,CAAC,CAAA;AACnC,IAAA,MAAME,kBAAkB,GAAGC,cAAc,CAACJ,UAAU,CAAC,CAAA;AACrD,IAAA,MAAMa,OAAO,GAAGR,eAAW,CAACF,kBAAkB,CAAC,GAC3CN,QAAQ,CAACiB,IAAI,CAAEP,CAAC,IAAKA,CAAC,CAACC,SAAS,KAAKL,kBAAkB,CAAC,GACxDN,QAAQ,CAACiB,IAAI,CAAEP,CAAC,IAAKA,CAAC,CAACE,IAAI,KAAKN,kBAAkB,CAAC,CAAA;IAEvD,IAAI,CAACU,OAAO,EAAE;AACZ,MAAA,MAAM,IAAIE,gCAAyB,CAACZ,kBAAkB,EAAEQ,OAAO,CAAC,CAAA;AAClE,KAAA;AAEA,IAAA,OAAOE,OAAO,CAAA;GACf,CAAA;EAED,MAAMG,YAAY,GAAG,CACnBhB,UAA8B,EAC9BiB,QAAyB,EACzBhB,aAA6B,KACf;IACd,IAAI;AACF,MAAA,OAAOS,GAAG,CAACV,UAAU,EAAEC,aAAa,CAAC,CAACO,SAAS,CAAA;KAChD,CAAC,OAAOU,KAAK,EAAE;AACd,MAAA,IAAID,QAAQ,KAAKE,SAAS,EAAE,MAAMD,KAAK,CAAA;MACvC,OAAOV,aAAS,CAACS,QAAQ,CAAC,CAAA;AAC5B,KAAA;GACD,CAAA;AAED,EAAA,MAAMf,GAAG,GAAG,CAACD,aAA4B,GAAG,SAAS,KAAgB;AACnE,IAAA,MAAMU,OAAO,GAAGC,oBAAoB,CAACX,aAAa,CAAC,CAAA;AACnD,IAAA,OAAOU,OAAO,KAAK,GAAG,GAClBd,QAAQ,GACRA,QAAQ,CAACuB,MAAM,CAAEP,OAAO,IAAKA,OAAO,CAACQ,WAAW,CAACV,OAAO,CAAC,CAAC,CAAA;GAC/D,CAAA;EAED,MAAMW,GAAG,GAAG,CAACT,OAAgB,EAAEU,SAAS,GAAG,IAAI,KAAW;IACxD,IAAI,CAACA,SAAS,IAAIxB,GAAG,CAACc,OAAO,CAACL,SAAS,EAAE,GAAG,CAAC,EAAE,OAAA;AAC/CX,IAAAA,QAAQ,CAAC2B,OAAO,CAACX,OAAO,CAAC,CAAA;GAC1B,CAAA;AAED,EAAA,MAAMY,IAAI,GAAG,CAACC,QAAgB,EAAEC,QAA4B,KAAW;AACrE7B,IAAAA,QAAQ,CAAC4B,QAAQ,CAAC,GAAGC,QAAQ,CAAA;AAC7BvB,IAAAA,cAAc,CAACsB,QAAQ,CAAC,CAAC;GAC1B,CAAA;;EAED,MAAME,MAAM,GAAIF,QAAgB,IAAW;IACzC,OAAO5B,QAAQ,CAAC4B,QAAQ,CAAC,CAAA;GAC1B,CAAA;EAED,MAAMG,KAAK,GAAG,MACZpC,8BAA8B,CAACC,OAAO,EAAEG,QAAQ,EAAEC,QAAQ,CAAC,CAAA;AAE7D,EAAA,MAAMgC,YAAY,GAAG,CACnBZ,KAAoB,EACpBa,WAAwB,KACA;AACxB;AACA,IAAA,IAAI,CAACC,KAAK,CAACC,OAAO,CAACf,KAAK,CAACgB,IAAI,CAAC,IAAIhB,KAAK,CAACgB,IAAI,CAACC,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI,CAAA;IACtE,MAAMD,IAAI,GAAGhB,KAAK,CAACgB,IAAI,CAACE,IAAI,CAAC,IAAI,CAAC,CAAA;;AAElC;IACA,MAAMC,gBAAgB,GAAG,qCAAqC,CAAA;AAC9D,IAAA,MAAMC,WAAW,GAAGpB,KAAK,CAACqB,OAAO,CAACC,KAAK,CAACH,gBAAgB,CAAC,GAAG,CAAC,CAAC,IAAI,IAAI,CAAA;;AAEtE;IACA,MAAMI,cAAc,GAAG,sCAAsC,CAAA;AAC7D,IAAA,MAAMC,eAAe,GAAGR,IAAI,CAACM,KAAK,CAACC,cAAc,CAAC,GAAG,CAAC,CAAC,IAAI,IAAI,CAAA;IAC/D,MAAME,SAAS,GAAGD,eAAe,GAAGE,QAAQ,CAACF,eAAe,EAAE,EAAE,CAAC,GAAG,IAAI,CAAA;;AAExE;IACA,IAAIJ,WAAW,KAAK,IAAI,IAAIK,SAAS,KAAK,IAAI,EAAE,OAAO,IAAI,CAAA;;AAE3D;AACA,IAAA,MAAME,iBAAyB,GAAGD,QAAQ,CAACN,WAAW,EAAE,EAAE,CAAC,CAAA;AAC3D,IAAA,MAAMQ,YAA2B,GAC/Bf,WAAW,CAACQ,OAAO,CAACQ,YAAY,GAAGF,iBAAiB,CAAC,EAAEC,YAAY,IACnE,IAAI,CAAA;AACN,IAAA,MAAME,SAAS,GAAGF,YAAY,GAC1Bf,WAAW,CAACQ,OAAO,CAACU,QAAQ,CAACH,YAAY,CAAC,GAC1C,IAAI,CAAA;;AAER;AACA,IAAA,IAAI,CAACE,SAAS,EAAE,OAAO,IAAI,CAAA;;AAE3B;AACA,IAAA,IAAInC,OAAgB,CAAA;IACpB,IAAI;AACFA,MAAAA,OAAO,GAAGH,GAAG,CAACsC,SAAS,CAAC,CAAA;KACzB,CAAC,OAAOE,qBAAqB,EAAE;AAC9B,MAAA,OAAO,IAAI,CAAA;AACb,KAAA;;AAEA;IACA,MAAMC,aAAa,GAAGtC,OAAO,CAACuC,gBAAgB,CAACT,SAAS,EAAEzB,KAAK,CAAC,CAAA;IAChE,OAAOiC,aAAa,IAAI,IAAIE,qCAA8B,CAACxC,OAAO,EAAEK,KAAK,CAAC,CAAA;GAC3E,CAAA;AAED,EAAA,MAAMN,oBAAoB,GAAIX,aAA4B,IACxDA,aAAa,KAAK,SAAS,GAAGP,OAAO,CAAC4D,GAAG,CAACC,UAAU,EAAE,GAAGtD,aAAa,CAAA;EAExE,MAAMG,cAAc,GAAG,CACrBJ,UAA8B,EAC9BwD,KAAe,GAAG,EAAE,KACG;AACvB,IAAA,IAAInD,eAAW,CAACL,UAAU,CAAC,EAAE,OAAOA,UAAU,CAAA;IAC9C,IAAIF,QAAQ,CAACE,UAAU,CAAC,KAAKmB,SAAS,EAAE,OAAOnB,UAAU,CAAA;AACzD,IAAA,MAAMyD,mBAAmB,GAAG,CAAC,GAAGD,KAAK,EAAExD,UAAU,CAAC,CAAA;AAClD,IAAA,IAAIwD,KAAK,CAACE,QAAQ,CAAC1D,UAAU,CAAC,EAAE;MAC9B,MAAM,IAAI2D,KAAK,CACZ,CAA6BF,2BAAAA,EAAAA,mBAAmB,CAACrB,IAAI,CAAC,MAAM,CAAE,CAAA,CAAC,CACjE,CAAA;AACH,KAAA;IACA,OAAOhC,cAAc,CAACN,QAAQ,CAACE,UAAU,CAAC,EAAEyD,mBAAmB,CAAC,CAAA;GACjE,CAAA;EAED,OAAO;IACL1D,GAAG;IACHW,GAAG;IACHM,YAAY;IACZd,GAAG;IACHoB,GAAG;IACHG,IAAI;IACJG,MAAM;IACNC,KAAK;AACLC,IAAAA,YAAAA;GACD,CAAA;AACH;;;;"}