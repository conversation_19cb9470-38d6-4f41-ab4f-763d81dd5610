{"version": 3, "file": "errors.mjs", "sources": ["../../src/errors.ts"], "sourcesContent": ["import {\n  Cluster,\n  Program,\n  ProgramError,\n  Public<PERSON>ey,\n  SdkError,\n  UnderlyingProgramError,\n  isPublic<PERSON>ey,\n} from '@metaplex-foundation/umi';\n\nexport class ProgramNotRecognizedError extends SdkError {\n  readonly name: string = 'ProgramNotRecognizedError';\n\n  readonly identifier: string | PublicKey;\n\n  readonly cluster: Cluster | '*';\n\n  constructor(identifier: string | PublicKey, cluster: Cluster | '*') {\n    const isName = !isPublicKey(identifier);\n    const toString = isName ? identifier : identifier;\n    const clusterString = cluster === '*' ? 'any' : `the [${cluster}]`;\n    const message =\n      `The provided program ${isName ? 'name' : 'address'} [${toString}] ` +\n      `is not recognized in ${clusterString} cluster. ` +\n      'Did you forget to register this program? ' +\n      'If so, you may use \"context.programs.add(myProgram)\" to fix this.';\n    super(message);\n    this.identifier = identifier;\n    this.cluster = cluster;\n  }\n}\n\n/** @category Errors */\nexport class ProgramErrorNotRecognizedError extends ProgramError {\n  readonly name: string = 'ProgramErrorNotRecognizedError';\n\n  constructor(program: Program, cause: UnderlyingProgramError) {\n    const ofCode = cause.code ? ` of code [${cause.code}]` : '';\n    const message =\n      `The program [${program.name}] ` +\n      `at address [${program.publicKey}] ` +\n      `raised an error${ofCode} ` +\n      `that is not recognized by the programs registered on the SDK. ` +\n      `Please check the underlying program error below for more details.`;\n    super(message, program, cause);\n  }\n}\n"], "names": ["ProgramNotRecognizedError", "SdkError", "name", "constructor", "identifier", "cluster", "isName", "isPublicKey", "toString", "clusterString", "message", "ProgramErrorNotRecognizedError", "ProgramError", "program", "cause", "ofCode", "code", "public<PERSON>ey"], "mappings": ";;AAUO,MAAMA,yBAAyB,SAASC,QAAQ,CAAC;AAC7CC,EAAAA,IAAI,GAAW,2BAA2B,CAAA;AAMnDC,EAAAA,WAAW,CAACC,UAA8B,EAAEC,OAAsB,EAAE;AAClE,IAAA,MAAMC,MAAM,GAAG,CAACC,WAAW,CAACH,UAAU,CAAC,CAAA;AACvC,IAAA,MAAMI,QAAQ,GAAGF,MAAM,GAAGF,UAAU,GAAGA,UAAU,CAAA;IACjD,MAAMK,aAAa,GAAGJ,OAAO,KAAK,GAAG,GAAG,KAAK,GAAI,CAAOA,KAAAA,EAAAA,OAAQ,CAAE,CAAA,CAAA,CAAA;AAClE,IAAA,MAAMK,OAAO,GACV,CAAA,qBAAA,EAAuBJ,MAAM,GAAG,MAAM,GAAG,SAAU,CAAA,EAAA,EAAIE,QAAS,CAAA,EAAA,CAAG,GACnE,CAAuBC,qBAAAA,EAAAA,aAAc,YAAW,GACjD,2CAA2C,GAC3C,mEAAmE,CAAA;IACrE,KAAK,CAACC,OAAO,CAAC,CAAA;IACd,IAAI,CAACN,UAAU,GAAGA,UAAU,CAAA;IAC5B,IAAI,CAACC,OAAO,GAAGA,OAAO,CAAA;AACxB,GAAA;AACF,CAAA;;AAEA;AACO,MAAMM,8BAA8B,SAASC,YAAY,CAAC;AACtDV,EAAAA,IAAI,GAAW,gCAAgC,CAAA;AAExDC,EAAAA,WAAW,CAACU,OAAgB,EAAEC,KAA6B,EAAE;AAC3D,IAAA,MAAMC,MAAM,GAAGD,KAAK,CAACE,IAAI,GAAI,CAAYF,UAAAA,EAAAA,KAAK,CAACE,IAAK,CAAE,CAAA,CAAA,GAAG,EAAE,CAAA;IAC3D,MAAMN,OAAO,GACV,CAAeG,aAAAA,EAAAA,OAAO,CAACX,IAAK,CAAA,EAAA,CAAG,GAC/B,CAAcW,YAAAA,EAAAA,OAAO,CAACI,SAAU,CAAA,EAAA,CAAG,GACnC,CAAiBF,eAAAA,EAAAA,MAAO,GAAE,GAC1B,CAAA,8DAAA,CAA+D,GAC/D,CAAkE,iEAAA,CAAA,CAAA;AACrE,IAAA,KAAK,CAACL,OAAO,EAAEG,OAAO,EAAEC,KAAK,CAAC,CAAA;AAChC,GAAA;AACF;;;;"}