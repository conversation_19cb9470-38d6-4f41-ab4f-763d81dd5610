{"version": 3, "file": "plugin.mjs", "sources": ["../../src/plugin.ts"], "sourcesContent": ["import { UmiPlugin } from '@metaplex-foundation/umi';\nimport { createDefaultProgramRepository } from './createDefaultProgramRepository';\n\nexport const defaultProgramRepository = (): UmiPlugin => ({\n  install(umi) {\n    umi.programs = createDefaultProgramRepository(umi);\n  },\n});\n"], "names": ["defaultProgramRepository", "install", "umi", "programs", "createDefaultProgramRepository"], "mappings": ";;AAGO,MAAMA,wBAAwB,GAAG,OAAkB;EACxDC,OAAO,CAACC,GAAG,EAAE;AACXA,IAAAA,GAAG,CAACC,QAAQ,GAAGC,8BAA8B,CAACF,GAAG,CAAC,CAAA;AACpD,GAAA;AACF,CAAC;;;;"}