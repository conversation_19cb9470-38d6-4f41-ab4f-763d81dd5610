{"version": 3, "file": "createChunkGetAccountsRpc.cjs", "sources": ["../../src/createChunkGetAccountsRpc.ts"], "sourcesContent": ["import { RpcInterface, chunk } from '@metaplex-foundation/umi';\n\nexport interface ChunkGetAccountsRpcOptions {\n  getAccountsChunkSize?: number;\n}\n\nexport const createChunkGetAccountsRpc = (\n  rpc: RpcInterface,\n  chunkSize = 100\n): RpcInterface => ({\n  ...rpc,\n  getAccounts: async (publicKeys, options) => {\n    const promises = chunk(publicKeys, chunkSize).map((chunk) =>\n      rpc.getAccounts(chunk, options)\n    );\n    const chunks = await Promise.all(promises);\n    return chunks.flat();\n  },\n});\n"], "names": ["createChunkGetAccountsRpc", "rpc", "chunkSize", "getAccounts", "publicKeys", "options", "promises", "chunk", "map", "chunks", "Promise", "all", "flat"], "mappings": ";;;;;;AAMO,MAAMA,yBAAyB,GAAG,CACvCC,GAAiB,EACjBC,SAAS,GAAG,GAAG,MACG;AAClB,EAAA,GAAGD,GAAG;AACNE,EAAAA,WAAW,EAAE,OAAOC,UAAU,EAAEC,OAAO,KAAK;IAC1C,MAAMC,QAAQ,GAAGC,SAAK,CAACH,UAAU,EAAEF,SAAS,CAAC,CAACM,GAAG,CAAED,KAAK,IACtDN,GAAG,CAACE,WAAW,CAACI,KAAK,EAAEF,OAAO,CAAC,CAChC,CAAA;IACD,MAAMI,MAAM,GAAG,MAAMC,OAAO,CAACC,GAAG,CAACL,QAAQ,CAAC,CAAA;IAC1C,OAAOG,MAAM,CAACG,IAAI,EAAE,CAAA;AACtB,GAAA;AACF,CAAC;;;;"}