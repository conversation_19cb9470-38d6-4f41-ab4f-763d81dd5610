{"version": 3, "file": "plugin.mjs", "sources": ["../../src/plugin.ts"], "sourcesContent": ["import { UmiPlugin } from '@metaplex-foundation/umi';\nimport { createChunkGetAccountsRpc } from './createChunkGetAccountsRpc';\n\nexport const chunkGetAccountsRpc = (chunkSize = 100): UmiPlugin => ({\n  install(umi) {\n    umi.rpc = createChunkGetAccountsRpc(umi.rpc, chunkSize);\n  },\n});\n"], "names": ["chunkGetAccountsRpc", "chunkSize", "install", "umi", "rpc", "createChunkGetAccountsRpc"], "mappings": ";;MAGaA,mBAAmB,GAAG,CAACC,SAAS,GAAG,GAAG,MAAiB;EAClEC,OAAO,CAACC,GAAG,EAAE;IACXA,GAAG,CAACC,GAAG,GAAGC,yBAAyB,CAACF,GAAG,CAACC,GAAG,EAAEH,SAAS,CAAC,CAAA;AACzD,GAAA;AACF,CAAC;;;;"}