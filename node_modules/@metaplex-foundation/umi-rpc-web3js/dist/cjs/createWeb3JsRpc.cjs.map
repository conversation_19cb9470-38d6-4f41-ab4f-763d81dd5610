{"version": 3, "file": "createWeb3JsRpc.cjs", "sources": ["../../src/createWeb3JsRpc.ts"], "sourcesContent": ["import {\n  ACCOUNT_HEADER_SIZE,\n  BlockhashWithExpiryBlockHeight,\n  Cluster,\n  Commitment,\n  CompiledInstruction,\n  Context,\n  createAmount,\n  DateTime,\n  dateTime,\n  ErrorWithLogs,\n  isZeroAmount,\n  lamports,\n  MaybeRpcAccount,\n  ProgramError,\n  PublicKey,\n  resolveClusterFromEndpoint,\n  RpcAccount,\n  RpcAccountExistsOptions,\n  RpcAirdropOptions,\n  RpcCallOptions,\n  RpcConfirmTransactionOptions,\n  RpcConfirmTransactionResult,\n  RpcDataFilter,\n  RpcGetAccountOptions,\n  RpcGetAccountsOptions,\n  RpcGetBalanceOptions,\n  RpcGetBlockTimeOptions,\n  RpcGetLatestBlockhashOptions,\n  RpcGetProgramAccountsOptions,\n  RpcGetRentOptions,\n  RpcGetSignatureStatusesOptions,\n  RpcGetSlotOptions,\n  RpcGetTransactionOptions,\n  RpcInterface,\n  RpcSendTransactionOptions,\n  RpcSimulateTransactionOptions,\n  RpcSimulateTransactionResult,\n  SolAmount,\n  Transaction,\n  TransactionMetaInnerInstruction,\n  TransactionMetaTokenBalance,\n  TransactionSignature,\n  TransactionStatus,\n  TransactionWithMeta,\n} from '@metaplex-foundation/umi';\nimport {\n  fromWeb3JsMessage,\n  fromWeb3JsPublicKey,\n  toWeb3JsPublicKey,\n  toWeb3JsTransaction,\n} from '@metaplex-foundation/umi-web3js-adapters';\nimport { base58 } from '@metaplex-foundation/umi/serializers';\nimport {\n  AccountInfo as Web3JsAccountInfo,\n  Connection as Web3JsConnection,\n  ConnectionConfig as Web3JsConnectionConfig,\n  GetProgramAccountsFilter as Web3JsGetProgramAccountsFilter,\n  PublicKey as Web3JsPublicKey,\n  TokenBalance as Web3JsTokenBalance,\n  TransactionConfirmationStrategy as Web3JsTransactionConfirmationStrategy,\n} from '@solana/web3.js';\nimport type { JSONRPCCallbackTypePlain } from 'jayson';\nimport type RpcClient from 'jayson/lib/client/browser';\n\nexport type Web3JsRpcOptions = Commitment | Web3JsConnectionConfig;\n\nexport function createWeb3JsRpc(\n  context: Pick<Context, 'programs' | 'transactions'>,\n  endpoint: string,\n  rpcOptions?: Web3JsRpcOptions\n): RpcInterface & { connection: Web3JsConnection };\nexport function createWeb3JsRpc(\n  context: Pick<Context, 'programs' | 'transactions'>,\n  connection: Web3JsConnection\n): RpcInterface & { connection: Web3JsConnection };\nexport function createWeb3JsRpc(\n  context: Pick<Context, 'programs' | 'transactions'>,\n  endpointOrConnection: string | Web3JsConnection,\n  rpcOptions?: Web3JsRpcOptions\n): RpcInterface & { connection: Web3JsConnection } {\n  let connection: Web3JsConnection | null = null;\n  const getConnection = () => {\n    if (connection) {\n      return connection;\n    }\n    if (typeof endpointOrConnection === 'string') {\n      connection = new Web3JsConnection(endpointOrConnection, rpcOptions);\n    } else {\n      connection = endpointOrConnection;\n    }\n    return connection;\n  };\n\n  const cluster = resolveClusterFromEndpoint(getConnection().rpcEndpoint);\n\n  const getAccount = async (\n    publicKey: PublicKey,\n    options: RpcGetAccountOptions = {}\n  ): Promise<MaybeRpcAccount> => {\n    const account = await getConnection().getAccountInfo(\n      toWeb3JsPublicKey(publicKey),\n      options\n    );\n    return parseMaybeAccount(account, publicKey);\n  };\n\n  const getAccounts = async (\n    publicKeys: PublicKey[],\n    options: RpcGetAccountsOptions = {}\n  ): Promise<MaybeRpcAccount[]> => {\n    const accounts = await getConnection().getMultipleAccountsInfo(\n      publicKeys.map(toWeb3JsPublicKey),\n      options\n    );\n    return accounts.map((account, index) =>\n      parseMaybeAccount(account, publicKeys[index])\n    );\n  };\n\n  const getProgramAccounts = async (\n    programId: PublicKey,\n    options: RpcGetProgramAccountsOptions = {}\n  ): Promise<RpcAccount[]> => {\n    const accounts = await getConnection().getProgramAccounts(\n      toWeb3JsPublicKey(programId),\n      {\n        ...options,\n        filters: options.filters?.map((filter) => parseDataFilter(filter)),\n      }\n    );\n    return accounts.map(({ pubkey, account }) =>\n      parseAccount(account, fromWeb3JsPublicKey(pubkey))\n    );\n  };\n\n  const getBlockTime = async (\n    slot: number,\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    _options: RpcGetBlockTimeOptions = {}\n  ): Promise<DateTime | null> => {\n    const blockTime = await getConnection().getBlockTime(slot);\n    return blockTime ? dateTime(blockTime) : null;\n  };\n\n  const getBalance = async (\n    publicKey: PublicKey,\n    options: RpcGetBalanceOptions = {}\n  ): Promise<SolAmount> => {\n    const balanceInLamports = await getConnection().getBalance(\n      toWeb3JsPublicKey(publicKey),\n      options\n    );\n    return lamports(balanceInLamports);\n  };\n\n  const getGenesisHash = async (): Promise<string> => {\n    const genesisHash = await getConnection().getGenesisHash();\n    return genesisHash;\n  };\n\n  const getRent = async (\n    bytes: number,\n    options: RpcGetRentOptions = {}\n  ): Promise<SolAmount> => {\n    const rentFor = (bytes: number) =>\n      getConnection().getMinimumBalanceForRentExemption(\n        bytes,\n        options.commitment\n      );\n    if (options.includesHeaderBytes ?? false) {\n      const headerRent = await rentFor(0);\n      const rentPerByte = BigInt(headerRent) / BigInt(ACCOUNT_HEADER_SIZE);\n      return lamports(rentPerByte * BigInt(bytes));\n    }\n    return lamports(await rentFor(bytes));\n  };\n\n  const getLatestBlockhash = async (\n    options: RpcGetLatestBlockhashOptions = {}\n  ): Promise<BlockhashWithExpiryBlockHeight> =>\n    getConnection().getLatestBlockhash(options);\n\n  const getTransaction = async (\n    signature: TransactionSignature,\n    options: RpcGetTransactionOptions = {}\n  ): Promise<TransactionWithMeta | null> => {\n    const response = await getConnection().getTransaction(\n      base58.deserialize(signature)[0],\n      {\n        commitment: options.commitment as 'confirmed' | 'finalized' | undefined,\n        maxSupportedTransactionVersion: 0,\n      }\n    );\n\n    if (!response) {\n      return null;\n    }\n\n    if (!response.meta) {\n      // TODO: Custom error.\n      throw new Error('Transaction meta is missing.');\n    }\n\n    const { transaction, meta } = response;\n    const message = fromWeb3JsMessage(transaction.message);\n    const mapPublicKey = (key: string) =>\n      fromWeb3JsPublicKey(new Web3JsPublicKey(key));\n    const mapTokenBalance = (\n      tokenBalance: Web3JsTokenBalance\n    ): TransactionMetaTokenBalance => ({\n      accountIndex: tokenBalance.accountIndex,\n      amount: createAmount(\n        tokenBalance.uiTokenAmount.amount,\n        'splToken',\n        tokenBalance.uiTokenAmount.decimals\n      ),\n      mint: mapPublicKey(tokenBalance.mint),\n      owner: tokenBalance.owner ? mapPublicKey(tokenBalance.owner) : null,\n    });\n\n    return {\n      message,\n      serializedMessage: context.transactions.serializeMessage(message),\n      signatures: transaction.signatures.map(base58.serialize),\n      meta: {\n        fee: lamports(meta.fee),\n        logs: meta.logMessages ?? [],\n        preBalances: meta.preBalances.map(lamports),\n        postBalances: meta.postBalances.map(lamports),\n        preTokenBalances: (meta.preTokenBalances ?? []).map(mapTokenBalance),\n        postTokenBalances: (meta.postTokenBalances ?? []).map(mapTokenBalance),\n        innerInstructions:\n          meta.innerInstructions?.map(\n            (inner): TransactionMetaInnerInstruction => ({\n              index: inner.index,\n              instructions: inner.instructions.map(\n                (instruction): CompiledInstruction => ({\n                  programIndex: instruction.programIdIndex,\n                  accountIndexes: instruction.accounts,\n                  data: base58.serialize(instruction.data),\n                })\n              ),\n            })\n          ) ?? null,\n        loadedAddresses: {\n          writable: (meta.loadedAddresses?.writable ?? []).map(\n            fromWeb3JsPublicKey\n          ),\n          readonly: (meta.loadedAddresses?.readonly ?? []).map(\n            fromWeb3JsPublicKey\n          ),\n        },\n        computeUnitsConsumed: meta.computeUnitsConsumed\n          ? BigInt(meta.computeUnitsConsumed)\n          : null,\n        err: meta.err,\n      },\n    };\n  };\n\n  const getSignatureStatuses = async (\n    signatures: TransactionSignature[],\n    options: RpcGetSignatureStatusesOptions = {}\n  ): Promise<Array<TransactionStatus | null>> => {\n    const response = await getConnection().getSignatureStatuses(\n      signatures.map((signature) => base58.deserialize(signature)[0]),\n      { searchTransactionHistory: options?.searchTransactionHistory ?? false }\n    );\n    return response.value.map((status) => {\n      if (!status) return null;\n      return {\n        slot: status.slot,\n        confirmations: status.confirmations,\n        error: status.err,\n        commitment: status.confirmationStatus ?? null,\n      };\n    });\n  };\n\n  const accountExists = async (\n    publicKey: PublicKey,\n    options: RpcAccountExistsOptions = {}\n  ): Promise<boolean> => !isZeroAmount(await getBalance(publicKey, options));\n\n  const airdrop = async (\n    publicKey: PublicKey,\n    amount: SolAmount,\n    options: RpcAirdropOptions = {}\n  ): Promise<void> => {\n    const signature = await getConnection().requestAirdrop(\n      toWeb3JsPublicKey(publicKey),\n      Number(amount.basisPoints)\n    );\n    if (options.strategy) {\n      await confirmTransaction(\n        base58.serialize(signature),\n        options as RpcConfirmTransactionOptions\n      );\n      return;\n    }\n    await confirmTransaction(base58.serialize(signature), {\n      ...options,\n      strategy: { type: 'blockhash', ...(await getLatestBlockhash()) },\n    });\n  };\n\n  const call = async <\n    Result,\n    Params extends any[] | Record<string, any> = any[]\n  >(\n    method: string,\n    params?: Params,\n    options: RpcCallOptions = {}\n  ): Promise<Result> => {\n    const client = (getConnection() as any)._rpcClient as RpcClient;\n\n    // Handle both array and object params\n    const resolvedParams = Array.isArray(params)\n      ? resolveCallParams(\n          [...params] as any[],\n          options.commitment,\n          options.extra\n        )\n      : resolveNamedCallParams(\n          params as Record<string, any>,\n          options.commitment,\n          options.extra\n        );\n\n    return new Promise((resolve, reject) => {\n      const callback: JSONRPCCallbackTypePlain = (error, response) => {\n        if (error) {\n          reject(error);\n        } else {\n          resolve(response.result);\n        }\n      };\n\n      if (options.id) {\n        client.request(method, resolvedParams, options.id, callback);\n      } else {\n        client.request(method, resolvedParams, callback);\n      }\n    });\n  };\n\n  const sendTransaction = async (\n    transaction: Transaction,\n    options: RpcSendTransactionOptions = {}\n  ): Promise<TransactionSignature> => {\n    try {\n      const signature = await getConnection().sendRawTransaction(\n        context.transactions.serialize(transaction),\n        options\n      );\n      return base58.serialize(signature);\n    } catch (error: any) {\n      let resolvedError: ProgramError | null = null;\n      if (error instanceof Error && 'logs' in error) {\n        resolvedError = context.programs.resolveError(\n          error as ErrorWithLogs,\n          transaction\n        );\n      }\n      throw resolvedError || error;\n    }\n  };\n\n  const simulateTransaction = async (\n    transaction: Transaction,\n    options: RpcSimulateTransactionOptions = {}\n  ): Promise<RpcSimulateTransactionResult> => {\n    try {\n      const tx = toWeb3JsTransaction(transaction);\n      const result = await getConnection().simulateTransaction(tx, {\n        sigVerify: options.verifySignatures,\n        accounts: {\n          addresses: options.accounts || [],\n          encoding: 'base64',\n        },\n      });\n      return result.value;\n    } catch (error: any) {\n      let resolvedError: ProgramError | null = null;\n      if (error instanceof Error && 'logs' in error) {\n        resolvedError = context.programs.resolveError(\n          error as ErrorWithLogs,\n          transaction\n        );\n      }\n      throw resolvedError || error;\n    }\n  };\n\n  const confirmTransaction = async (\n    signature: TransactionSignature,\n    options: RpcConfirmTransactionOptions\n  ): Promise<RpcConfirmTransactionResult> =>\n    getConnection().confirmTransaction(\n      parseConfirmStrategy(signature, options),\n      options.commitment\n    );\n\n  return {\n    getEndpoint: (): string => getConnection().rpcEndpoint,\n    getCluster: (): Cluster => cluster,\n    getAccount,\n    getAccounts,\n    getProgramAccounts,\n    getBlockTime,\n    getGenesisHash,\n    getBalance,\n    getRent,\n    getSlot: async (options: RpcGetSlotOptions = {}) =>\n      getConnection().getSlot(options),\n    getLatestBlockhash,\n    getTransaction,\n    getSignatureStatuses,\n    accountExists,\n    airdrop,\n    call,\n    sendTransaction,\n    simulateTransaction,\n    confirmTransaction,\n    get connection() {\n      return getConnection();\n    },\n  };\n}\n\nfunction parseAccount(\n  account: Web3JsAccountInfo<Uint8Array>,\n  publicKey: PublicKey\n): RpcAccount {\n  return {\n    executable: account.executable,\n    owner: fromWeb3JsPublicKey(account.owner),\n    lamports: lamports(account.lamports),\n    rentEpoch: account.rentEpoch ? BigInt(account.rentEpoch) : undefined,\n    publicKey,\n    data: new Uint8Array(account.data),\n  };\n}\n\nfunction parseMaybeAccount(\n  account: Web3JsAccountInfo<Uint8Array> | null,\n  publicKey: PublicKey\n): MaybeRpcAccount {\n  return account\n    ? { ...parseAccount(account, publicKey), exists: true }\n    : { exists: false, publicKey };\n}\n\nfunction parseDataFilter(\n  filter: RpcDataFilter\n): Web3JsGetProgramAccountsFilter {\n  if (!('memcmp' in filter)) return filter;\n  const { bytes, ...rest } = filter.memcmp;\n  return { memcmp: { ...rest, bytes: base58.deserialize(bytes)[0] } };\n}\n\nfunction parseConfirmStrategy(\n  signature: TransactionSignature,\n  options: RpcConfirmTransactionOptions\n): Web3JsTransactionConfirmationStrategy {\n  if (options.strategy.type === 'blockhash') {\n    return {\n      ...options.strategy,\n      signature: base58.deserialize(signature)[0],\n    };\n  }\n  return {\n    ...options.strategy,\n    signature: base58.deserialize(signature)[0],\n    nonceAccountPubkey: toWeb3JsPublicKey(options.strategy.nonceAccountPubkey),\n  };\n}\n\nfunction resolveCallParams<Params extends any[]>(\n  args: Params,\n  commitment?: Commitment,\n  extra?: object\n): Params {\n  if (!commitment && !extra) return args;\n  let options: any = {};\n  if (commitment) options.commitment = commitment;\n  if (extra) options = { ...options, ...extra };\n  args.push(options);\n  return args;\n}\n\nfunction resolveNamedCallParams(\n  params: Record<string, any> = {},\n  commitment?: Commitment,\n  extra?: object\n): Record<string, any> {\n  if (!commitment && !extra) return params;\n\n  // Create a new object with all original parameters\n  const result = { ...params };\n\n  // Add commitment and extra options directly into the params object\n  if (commitment) result.commitment = commitment;\n  if (extra) Object.assign(result, extra);\n\n  return result;\n}\n"], "names": ["createWeb3JsRpc", "context", "endpointOrConnection", "rpcOptions", "connection", "getConnection", "Web3JsConnection", "cluster", "resolveClusterFromEndpoint", "rpcEndpoint", "getAccount", "public<PERSON>ey", "options", "account", "getAccountInfo", "toWeb3JsPublicKey", "parseMaybeAccount", "getAccounts", "publicKeys", "accounts", "getMultipleAccountsInfo", "map", "index", "getProgramAccounts", "programId", "filters", "filter", "parseDataFilter", "pubkey", "parseAccount", "fromWeb3JsPublicKey", "getBlockTime", "slot", "_options", "blockTime", "dateTime", "getBalance", "balanceInLamports", "lamports", "getGenesisHash", "genesisHash", "getRent", "bytes", "rentFor", "getMinimumBalanceForRentExemption", "commitment", "includesHeaderBytes", "headerRent", "rentPerByte", "BigInt", "ACCOUNT_HEADER_SIZE", "getLatestBlockhash", "getTransaction", "signature", "response", "base58", "deserialize", "maxSupportedTransactionVersion", "meta", "Error", "transaction", "message", "fromWeb3JsMessage", "mapPublicKey", "key", "Web3JsPublicKey", "mapTokenBalance", "tokenBalance", "accountIndex", "amount", "createAmount", "uiTokenAmount", "decimals", "mint", "owner", "serializedMessage", "transactions", "serializeMessage", "signatures", "serialize", "fee", "logs", "logMessages", "preBalances", "postBalances", "preTokenBalances", "postTokenBalances", "innerInstructions", "inner", "instructions", "instruction", "programIndex", "programIdIndex", "accountIndexes", "data", "loadedAddresses", "writable", "readonly", "computeUnitsConsumed", "err", "getSignatureStatuses", "searchTransactionHistory", "value", "status", "confirmations", "error", "confirmation<PERSON>tatus", "accountExists", "isZeroAmount", "airdrop", "requestAirdrop", "Number", "basisPoints", "strategy", "confirmTransaction", "type", "call", "method", "params", "client", "_rpcClient", "resolvedParams", "Array", "isArray", "resolveCallParams", "extra", "resolveNamedCallParams", "Promise", "resolve", "reject", "callback", "result", "id", "request", "sendTransaction", "sendRawTransaction", "resolvedError", "programs", "resolveError", "simulateTransaction", "tx", "toWeb3JsTransaction", "sigVerify", "verifySignatures", "addresses", "encoding", "parseConfirmStrategy", "getEndpoint", "getCluster", "getSlot", "executable", "rentEpoch", "undefined", "Uint8Array", "exists", "rest", "memcmp", "nonceAccount<PERSON><PERSON>key", "args", "push", "Object", "assign"], "mappings": ";;;;;;;;;AA4EO,SAASA,eAAe,CAC7BC,OAAmD,EACnDC,oBAA+C,EAC/CC,UAA6B,EACoB;EACjD,IAAIC,UAAmC,GAAG,IAAI,CAAA;EAC9C,MAAMC,aAAa,GAAG,MAAM;AAC1B,IAAA,IAAID,UAAU,EAAE;AACd,MAAA,OAAOA,UAAU,CAAA;AACnB,KAAA;AACA,IAAA,IAAI,OAAOF,oBAAoB,KAAK,QAAQ,EAAE;AAC5CE,MAAAA,UAAU,GAAG,IAAIE,kBAAgB,CAACJ,oBAAoB,EAAEC,UAAU,CAAC,CAAA;AACrE,KAAC,MAAM;AACLC,MAAAA,UAAU,GAAGF,oBAAoB,CAAA;AACnC,KAAA;AACA,IAAA,OAAOE,UAAU,CAAA;GAClB,CAAA;EAED,MAAMG,OAAO,GAAGC,8BAA0B,CAACH,aAAa,EAAE,CAACI,WAAW,CAAC,CAAA;EAEvE,MAAMC,UAAU,GAAG,OACjBC,SAAoB,EACpBC,OAA6B,GAAG,EAAE,KACL;AAC7B,IAAA,MAAMC,OAAO,GAAG,MAAMR,aAAa,EAAE,CAACS,cAAc,CAClDC,mCAAiB,CAACJ,SAAS,CAAC,EAC5BC,OAAO,CACR,CAAA;AACD,IAAA,OAAOI,iBAAiB,CAACH,OAAO,EAAEF,SAAS,CAAC,CAAA;GAC7C,CAAA;EAED,MAAMM,WAAW,GAAG,OAClBC,UAAuB,EACvBN,OAA8B,GAAG,EAAE,KACJ;AAC/B,IAAA,MAAMO,QAAQ,GAAG,MAAMd,aAAa,EAAE,CAACe,uBAAuB,CAC5DF,UAAU,CAACG,GAAG,CAACN,mCAAiB,CAAC,EACjCH,OAAO,CACR,CAAA;AACD,IAAA,OAAOO,QAAQ,CAACE,GAAG,CAAC,CAACR,OAAO,EAAES,KAAK,KACjCN,iBAAiB,CAACH,OAAO,EAAEK,UAAU,CAACI,KAAK,CAAC,CAAC,CAC9C,CAAA;GACF,CAAA;EAED,MAAMC,kBAAkB,GAAG,OACzBC,SAAoB,EACpBZ,OAAqC,GAAG,EAAE,KAChB;AAC1B,IAAA,MAAMO,QAAQ,GAAG,MAAMd,aAAa,EAAE,CAACkB,kBAAkB,CACvDR,mCAAiB,CAACS,SAAS,CAAC,EAC5B;AACE,MAAA,GAAGZ,OAAO;AACVa,MAAAA,OAAO,EAAEb,OAAO,CAACa,OAAO,EAAEJ,GAAG,CAAEK,MAAM,IAAKC,eAAe,CAACD,MAAM,CAAC,CAAA;AACnE,KAAC,CACF,CAAA;AACD,IAAA,OAAOP,QAAQ,CAACE,GAAG,CAAC,CAAC;MAAEO,MAAM;AAAEf,MAAAA,OAAAA;KAAS,KACtCgB,YAAY,CAAChB,OAAO,EAAEiB,qCAAmB,CAACF,MAAM,CAAC,CAAC,CACnD,CAAA;GACF,CAAA;EAED,MAAMG,YAAY,GAAG,OACnBC,IAAY;AACZ;EACAC,QAAgC,GAAG,EAAE,KACR;IAC7B,MAAMC,SAAS,GAAG,MAAM7B,aAAa,EAAE,CAAC0B,YAAY,CAACC,IAAI,CAAC,CAAA;AAC1D,IAAA,OAAOE,SAAS,GAAGC,YAAQ,CAACD,SAAS,CAAC,GAAG,IAAI,CAAA;GAC9C,CAAA;EAED,MAAME,UAAU,GAAG,OACjBzB,SAAoB,EACpBC,OAA6B,GAAG,EAAE,KACX;AACvB,IAAA,MAAMyB,iBAAiB,GAAG,MAAMhC,aAAa,EAAE,CAAC+B,UAAU,CACxDrB,mCAAiB,CAACJ,SAAS,CAAC,EAC5BC,OAAO,CACR,CAAA;IACD,OAAO0B,YAAQ,CAACD,iBAAiB,CAAC,CAAA;GACnC,CAAA;EAED,MAAME,cAAc,GAAG,YAA6B;AAClD,IAAA,MAAMC,WAAW,GAAG,MAAMnC,aAAa,EAAE,CAACkC,cAAc,EAAE,CAAA;AAC1D,IAAA,OAAOC,WAAW,CAAA;GACnB,CAAA;EAED,MAAMC,OAAO,GAAG,OACdC,KAAa,EACb9B,OAA0B,GAAG,EAAE,KACR;AACvB,IAAA,MAAM+B,OAAO,GAAID,KAAa,IAC5BrC,aAAa,EAAE,CAACuC,iCAAiC,CAC/CF,KAAK,EACL9B,OAAO,CAACiC,UAAU,CACnB,CAAA;AACH,IAAA,IAAIjC,OAAO,CAACkC,mBAAmB,IAAI,KAAK,EAAE;AACxC,MAAA,MAAMC,UAAU,GAAG,MAAMJ,OAAO,CAAC,CAAC,CAAC,CAAA;MACnC,MAAMK,WAAW,GAAGC,MAAM,CAACF,UAAU,CAAC,GAAGE,MAAM,CAACC,uBAAmB,CAAC,CAAA;MACpE,OAAOZ,YAAQ,CAACU,WAAW,GAAGC,MAAM,CAACP,KAAK,CAAC,CAAC,CAAA;AAC9C,KAAA;AACA,IAAA,OAAOJ,YAAQ,CAAC,MAAMK,OAAO,CAACD,KAAK,CAAC,CAAC,CAAA;GACtC,CAAA;AAED,EAAA,MAAMS,kBAAkB,GAAG,OACzBvC,OAAqC,GAAG,EAAE,KAE1CP,aAAa,EAAE,CAAC8C,kBAAkB,CAACvC,OAAO,CAAC,CAAA;EAE7C,MAAMwC,cAAc,GAAG,OACrBC,SAA+B,EAC/BzC,OAAiC,GAAG,EAAE,KACE;AACxC,IAAA,MAAM0C,QAAQ,GAAG,MAAMjD,aAAa,EAAE,CAAC+C,cAAc,CACnDG,kBAAM,CAACC,WAAW,CAACH,SAAS,CAAC,CAAC,CAAC,CAAC,EAChC;MACER,UAAU,EAAEjC,OAAO,CAACiC,UAAmD;AACvEY,MAAAA,8BAA8B,EAAE,CAAA;AAClC,KAAC,CACF,CAAA;IAED,IAAI,CAACH,QAAQ,EAAE;AACb,MAAA,OAAO,IAAI,CAAA;AACb,KAAA;AAEA,IAAA,IAAI,CAACA,QAAQ,CAACI,IAAI,EAAE;AAClB;AACA,MAAA,MAAM,IAAIC,KAAK,CAAC,8BAA8B,CAAC,CAAA;AACjD,KAAA;IAEA,MAAM;MAAEC,WAAW;AAAEF,MAAAA,IAAAA;AAAK,KAAC,GAAGJ,QAAQ,CAAA;AACtC,IAAA,MAAMO,OAAO,GAAGC,mCAAiB,CAACF,WAAW,CAACC,OAAO,CAAC,CAAA;IACtD,MAAME,YAAY,GAAIC,GAAW,IAC/BlC,qCAAmB,CAAC,IAAImC,iBAAe,CAACD,GAAG,CAAC,CAAC,CAAA;IAC/C,MAAME,eAAe,GACnBC,YAAgC,KACC;MACjCC,YAAY,EAAED,YAAY,CAACC,YAAY;AACvCC,MAAAA,MAAM,EAAEC,gBAAY,CAClBH,YAAY,CAACI,aAAa,CAACF,MAAM,EACjC,UAAU,EACVF,YAAY,CAACI,aAAa,CAACC,QAAQ,CACpC;AACDC,MAAAA,IAAI,EAAEV,YAAY,CAACI,YAAY,CAACM,IAAI,CAAC;MACrCC,KAAK,EAAEP,YAAY,CAACO,KAAK,GAAGX,YAAY,CAACI,YAAY,CAACO,KAAK,CAAC,GAAG,IAAA;AACjE,KAAC,CAAC,CAAA;IAEF,OAAO;MACLb,OAAO;MACPc,iBAAiB,EAAE1E,OAAO,CAAC2E,YAAY,CAACC,gBAAgB,CAAChB,OAAO,CAAC;MACjEiB,UAAU,EAAElB,WAAW,CAACkB,UAAU,CAACzD,GAAG,CAACkC,kBAAM,CAACwB,SAAS,CAAC;AACxDrB,MAAAA,IAAI,EAAE;AACJsB,QAAAA,GAAG,EAAE1C,YAAQ,CAACoB,IAAI,CAACsB,GAAG,CAAC;AACvBC,QAAAA,IAAI,EAAEvB,IAAI,CAACwB,WAAW,IAAI,EAAE;QAC5BC,WAAW,EAAEzB,IAAI,CAACyB,WAAW,CAAC9D,GAAG,CAACiB,YAAQ,CAAC;QAC3C8C,YAAY,EAAE1B,IAAI,CAAC0B,YAAY,CAAC/D,GAAG,CAACiB,YAAQ,CAAC;QAC7C+C,gBAAgB,EAAE,CAAC3B,IAAI,CAAC2B,gBAAgB,IAAI,EAAE,EAAEhE,GAAG,CAAC6C,eAAe,CAAC;QACpEoB,iBAAiB,EAAE,CAAC5B,IAAI,CAAC4B,iBAAiB,IAAI,EAAE,EAAEjE,GAAG,CAAC6C,eAAe,CAAC;QACtEqB,iBAAiB,EACf7B,IAAI,CAAC6B,iBAAiB,EAAElE,GAAG,CACxBmE,KAAK,KAAuC;UAC3ClE,KAAK,EAAEkE,KAAK,CAAClE,KAAK;UAClBmE,YAAY,EAAED,KAAK,CAACC,YAAY,CAACpE,GAAG,CACjCqE,WAAW,KAA2B;YACrCC,YAAY,EAAED,WAAW,CAACE,cAAc;YACxCC,cAAc,EAAEH,WAAW,CAACvE,QAAQ;AACpC2E,YAAAA,IAAI,EAAEvC,kBAAM,CAACwB,SAAS,CAACW,WAAW,CAACI,IAAI,CAAA;AACzC,WAAC,CAAC,CAAA;SAEL,CAAC,CACH,IAAI,IAAI;AACXC,QAAAA,eAAe,EAAE;AACfC,UAAAA,QAAQ,EAAE,CAACtC,IAAI,CAACqC,eAAe,EAAEC,QAAQ,IAAI,EAAE,EAAE3E,GAAG,CAClDS,qCAAmB,CACpB;AACDmE,UAAAA,QAAQ,EAAE,CAACvC,IAAI,CAACqC,eAAe,EAAEE,QAAQ,IAAI,EAAE,EAAE5E,GAAG,CAClDS,qCAAmB,CAAA;SAEtB;AACDoE,QAAAA,oBAAoB,EAAExC,IAAI,CAACwC,oBAAoB,GAC3CjD,MAAM,CAACS,IAAI,CAACwC,oBAAoB,CAAC,GACjC,IAAI;QACRC,GAAG,EAAEzC,IAAI,CAACyC,GAAAA;AACZ,OAAA;KACD,CAAA;GACF,CAAA;EAED,MAAMC,oBAAoB,GAAG,OAC3BtB,UAAkC,EAClClE,OAAuC,GAAG,EAAE,KACC;IAC7C,MAAM0C,QAAQ,GAAG,MAAMjD,aAAa,EAAE,CAAC+F,oBAAoB,CACzDtB,UAAU,CAACzD,GAAG,CAAEgC,SAAS,IAAKE,kBAAM,CAACC,WAAW,CAACH,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,EAC/D;AAAEgD,MAAAA,wBAAwB,EAAEzF,OAAO,EAAEyF,wBAAwB,IAAI,KAAA;AAAM,KAAC,CACzE,CAAA;AACD,IAAA,OAAO/C,QAAQ,CAACgD,KAAK,CAACjF,GAAG,CAAEkF,MAAM,IAAK;AACpC,MAAA,IAAI,CAACA,MAAM,EAAE,OAAO,IAAI,CAAA;MACxB,OAAO;QACLvE,IAAI,EAAEuE,MAAM,CAACvE,IAAI;QACjBwE,aAAa,EAAED,MAAM,CAACC,aAAa;QACnCC,KAAK,EAAEF,MAAM,CAACJ,GAAG;AACjBtD,QAAAA,UAAU,EAAE0D,MAAM,CAACG,kBAAkB,IAAI,IAAA;OAC1C,CAAA;AACH,KAAC,CAAC,CAAA;GACH,CAAA;EAED,MAAMC,aAAa,GAAG,OACpBhG,SAAoB,EACpBC,OAAgC,GAAG,EAAE,KAChB,CAACgG,gBAAY,CAAC,MAAMxE,UAAU,CAACzB,SAAS,EAAEC,OAAO,CAAC,CAAC,CAAA;EAE1E,MAAMiG,OAAO,GAAG,OACdlG,SAAoB,EACpB0D,MAAiB,EACjBzD,OAA0B,GAAG,EAAE,KACb;AAClB,IAAA,MAAMyC,SAAS,GAAG,MAAMhD,aAAa,EAAE,CAACyG,cAAc,CACpD/F,mCAAiB,CAACJ,SAAS,CAAC,EAC5BoG,MAAM,CAAC1C,MAAM,CAAC2C,WAAW,CAAC,CAC3B,CAAA;IACD,IAAIpG,OAAO,CAACqG,QAAQ,EAAE;MACpB,MAAMC,kBAAkB,CACtB3D,kBAAM,CAACwB,SAAS,CAAC1B,SAAS,CAAC,EAC3BzC,OAAO,CACR,CAAA;AACD,MAAA,OAAA;AACF,KAAA;IACA,MAAMsG,kBAAkB,CAAC3D,kBAAM,CAACwB,SAAS,CAAC1B,SAAS,CAAC,EAAE;AACpD,MAAA,GAAGzC,OAAO;AACVqG,MAAAA,QAAQ,EAAE;AAAEE,QAAAA,IAAI,EAAE,WAAW;QAAE,IAAI,MAAMhE,kBAAkB,EAAE,CAAA;AAAE,OAAA;AACjE,KAAC,CAAC,CAAA;GACH,CAAA;EAED,MAAMiE,IAAI,GAAG,OAIXC,MAAc,EACdC,MAAe,EACf1G,OAAuB,GAAG,EAAE,KACR;AACpB,IAAA,MAAM2G,MAAM,GAAIlH,aAAa,EAAE,CAASmH,UAAuB,CAAA;;AAE/D;AACA,IAAA,MAAMC,cAAc,GAAGC,KAAK,CAACC,OAAO,CAACL,MAAM,CAAC,GACxCM,iBAAiB,CACf,CAAC,GAAGN,MAAM,CAAC,EACX1G,OAAO,CAACiC,UAAU,EAClBjC,OAAO,CAACiH,KAAK,CACd,GACDC,sBAAsB,CACpBR,MAAM,EACN1G,OAAO,CAACiC,UAAU,EAClBjC,OAAO,CAACiH,KAAK,CACd,CAAA;AAEL,IAAA,OAAO,IAAIE,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;AACtC,MAAA,MAAMC,QAAkC,GAAG,CAACzB,KAAK,EAAEnD,QAAQ,KAAK;AAC9D,QAAA,IAAImD,KAAK,EAAE;UACTwB,MAAM,CAACxB,KAAK,CAAC,CAAA;AACf,SAAC,MAAM;AACLuB,UAAAA,OAAO,CAAC1E,QAAQ,CAAC6E,MAAM,CAAC,CAAA;AAC1B,SAAA;OACD,CAAA;MAED,IAAIvH,OAAO,CAACwH,EAAE,EAAE;AACdb,QAAAA,MAAM,CAACc,OAAO,CAAChB,MAAM,EAAEI,cAAc,EAAE7G,OAAO,CAACwH,EAAE,EAAEF,QAAQ,CAAC,CAAA;AAC9D,OAAC,MAAM;QACLX,MAAM,CAACc,OAAO,CAAChB,MAAM,EAAEI,cAAc,EAAES,QAAQ,CAAC,CAAA;AAClD,OAAA;AACF,KAAC,CAAC,CAAA;GACH,CAAA;EAED,MAAMI,eAAe,GAAG,OACtB1E,WAAwB,EACxBhD,OAAkC,GAAG,EAAE,KACL;IAClC,IAAI;AACF,MAAA,MAAMyC,SAAS,GAAG,MAAMhD,aAAa,EAAE,CAACkI,kBAAkB,CACxDtI,OAAO,CAAC2E,YAAY,CAACG,SAAS,CAACnB,WAAW,CAAC,EAC3ChD,OAAO,CACR,CAAA;AACD,MAAA,OAAO2C,kBAAM,CAACwB,SAAS,CAAC1B,SAAS,CAAC,CAAA;KACnC,CAAC,OAAOoD,KAAU,EAAE;MACnB,IAAI+B,aAAkC,GAAG,IAAI,CAAA;AAC7C,MAAA,IAAI/B,KAAK,YAAY9C,KAAK,IAAI,MAAM,IAAI8C,KAAK,EAAE;QAC7C+B,aAAa,GAAGvI,OAAO,CAACwI,QAAQ,CAACC,YAAY,CAC3CjC,KAAK,EACL7C,WAAW,CACZ,CAAA;AACH,OAAA;MACA,MAAM4E,aAAa,IAAI/B,KAAK,CAAA;AAC9B,KAAA;GACD,CAAA;EAED,MAAMkC,mBAAmB,GAAG,OAC1B/E,WAAwB,EACxBhD,OAAsC,GAAG,EAAE,KACD;IAC1C,IAAI;AACF,MAAA,MAAMgI,EAAE,GAAGC,qCAAmB,CAACjF,WAAW,CAAC,CAAA;MAC3C,MAAMuE,MAAM,GAAG,MAAM9H,aAAa,EAAE,CAACsI,mBAAmB,CAACC,EAAE,EAAE;QAC3DE,SAAS,EAAElI,OAAO,CAACmI,gBAAgB;AACnC5H,QAAAA,QAAQ,EAAE;AACR6H,UAAAA,SAAS,EAAEpI,OAAO,CAACO,QAAQ,IAAI,EAAE;AACjC8H,UAAAA,QAAQ,EAAE,QAAA;AACZ,SAAA;AACF,OAAC,CAAC,CAAA;MACF,OAAOd,MAAM,CAAC7B,KAAK,CAAA;KACpB,CAAC,OAAOG,KAAU,EAAE;MACnB,IAAI+B,aAAkC,GAAG,IAAI,CAAA;AAC7C,MAAA,IAAI/B,KAAK,YAAY9C,KAAK,IAAI,MAAM,IAAI8C,KAAK,EAAE;QAC7C+B,aAAa,GAAGvI,OAAO,CAACwI,QAAQ,CAACC,YAAY,CAC3CjC,KAAK,EACL7C,WAAW,CACZ,CAAA;AACH,OAAA;MACA,MAAM4E,aAAa,IAAI/B,KAAK,CAAA;AAC9B,KAAA;GACD,CAAA;EAED,MAAMS,kBAAkB,GAAG,OACzB7D,SAA+B,EAC/BzC,OAAqC,KAErCP,aAAa,EAAE,CAAC6G,kBAAkB,CAChCgC,oBAAoB,CAAC7F,SAAS,EAAEzC,OAAO,CAAC,EACxCA,OAAO,CAACiC,UAAU,CACnB,CAAA;EAEH,OAAO;AACLsG,IAAAA,WAAW,EAAE,MAAc9I,aAAa,EAAE,CAACI,WAAW;IACtD2I,UAAU,EAAE,MAAe7I,OAAO;IAClCG,UAAU;IACVO,WAAW;IACXM,kBAAkB;IAClBQ,YAAY;IACZQ,cAAc;IACdH,UAAU;IACVK,OAAO;AACP4G,IAAAA,OAAO,EAAE,OAAOzI,OAA0B,GAAG,EAAE,KAC7CP,aAAa,EAAE,CAACgJ,OAAO,CAACzI,OAAO,CAAC;IAClCuC,kBAAkB;IAClBC,cAAc;IACdgD,oBAAoB;IACpBO,aAAa;IACbE,OAAO;IACPO,IAAI;IACJkB,eAAe;IACfK,mBAAmB;IACnBzB,kBAAkB;AAClB,IAAA,IAAI9G,UAAU,GAAG;AACf,MAAA,OAAOC,aAAa,EAAE,CAAA;AACxB,KAAA;GACD,CAAA;AACH,CAAA;AAEA,SAASwB,YAAY,CACnBhB,OAAsC,EACtCF,SAAoB,EACR;EACZ,OAAO;IACL2I,UAAU,EAAEzI,OAAO,CAACyI,UAAU;AAC9B5E,IAAAA,KAAK,EAAE5C,qCAAmB,CAACjB,OAAO,CAAC6D,KAAK,CAAC;AACzCpC,IAAAA,QAAQ,EAAEA,YAAQ,CAACzB,OAAO,CAACyB,QAAQ,CAAC;AACpCiH,IAAAA,SAAS,EAAE1I,OAAO,CAAC0I,SAAS,GAAGtG,MAAM,CAACpC,OAAO,CAAC0I,SAAS,CAAC,GAAGC,SAAS;IACpE7I,SAAS;AACTmF,IAAAA,IAAI,EAAE,IAAI2D,UAAU,CAAC5I,OAAO,CAACiF,IAAI,CAAA;GAClC,CAAA;AACH,CAAA;AAEA,SAAS9E,iBAAiB,CACxBH,OAA6C,EAC7CF,SAAoB,EACH;AACjB,EAAA,OAAOE,OAAO,GACV;AAAE,IAAA,GAAGgB,YAAY,CAAChB,OAAO,EAAEF,SAAS,CAAC;AAAE+I,IAAAA,MAAM,EAAE,IAAA;AAAK,GAAC,GACrD;AAAEA,IAAAA,MAAM,EAAE,KAAK;AAAE/I,IAAAA,SAAAA;GAAW,CAAA;AAClC,CAAA;AAEA,SAASgB,eAAe,CACtBD,MAAqB,EACW;AAChC,EAAA,IAAI,EAAE,QAAQ,IAAIA,MAAM,CAAC,EAAE,OAAOA,MAAM,CAAA;EACxC,MAAM;IAAEgB,KAAK;IAAE,GAAGiH,IAAAA;GAAM,GAAGjI,MAAM,CAACkI,MAAM,CAAA;EACxC,OAAO;AAAEA,IAAAA,MAAM,EAAE;AAAE,MAAA,GAAGD,IAAI;MAAEjH,KAAK,EAAEa,kBAAM,CAACC,WAAW,CAACd,KAAK,CAAC,CAAC,CAAC,CAAA;AAAE,KAAA;GAAG,CAAA;AACrE,CAAA;AAEA,SAASwG,oBAAoB,CAC3B7F,SAA+B,EAC/BzC,OAAqC,EACE;AACvC,EAAA,IAAIA,OAAO,CAACqG,QAAQ,CAACE,IAAI,KAAK,WAAW,EAAE;IACzC,OAAO;MACL,GAAGvG,OAAO,CAACqG,QAAQ;MACnB5D,SAAS,EAAEE,kBAAM,CAACC,WAAW,CAACH,SAAS,CAAC,CAAC,CAAC,CAAA;KAC3C,CAAA;AACH,GAAA;EACA,OAAO;IACL,GAAGzC,OAAO,CAACqG,QAAQ;IACnB5D,SAAS,EAAEE,kBAAM,CAACC,WAAW,CAACH,SAAS,CAAC,CAAC,CAAC,CAAC;AAC3CwG,IAAAA,kBAAkB,EAAE9I,mCAAiB,CAACH,OAAO,CAACqG,QAAQ,CAAC4C,kBAAkB,CAAA;GAC1E,CAAA;AACH,CAAA;AAEA,SAASjC,iBAAiB,CACxBkC,IAAY,EACZjH,UAAuB,EACvBgF,KAAc,EACN;AACR,EAAA,IAAI,CAAChF,UAAU,IAAI,CAACgF,KAAK,EAAE,OAAOiC,IAAI,CAAA;EACtC,IAAIlJ,OAAY,GAAG,EAAE,CAAA;AACrB,EAAA,IAAIiC,UAAU,EAAEjC,OAAO,CAACiC,UAAU,GAAGA,UAAU,CAAA;EAC/C,IAAIgF,KAAK,EAAEjH,OAAO,GAAG;AAAE,IAAA,GAAGA,OAAO;IAAE,GAAGiH,KAAAA;GAAO,CAAA;AAC7CiC,EAAAA,IAAI,CAACC,IAAI,CAACnJ,OAAO,CAAC,CAAA;AAClB,EAAA,OAAOkJ,IAAI,CAAA;AACb,CAAA;AAEA,SAAShC,sBAAsB,CAC7BR,MAA2B,GAAG,EAAE,EAChCzE,UAAuB,EACvBgF,KAAc,EACO;AACrB,EAAA,IAAI,CAAChF,UAAU,IAAI,CAACgF,KAAK,EAAE,OAAOP,MAAM,CAAA;;AAExC;AACA,EAAA,MAAMa,MAAM,GAAG;IAAE,GAAGb,MAAAA;GAAQ,CAAA;;AAE5B;AACA,EAAA,IAAIzE,UAAU,EAAEsF,MAAM,CAACtF,UAAU,GAAGA,UAAU,CAAA;EAC9C,IAAIgF,KAAK,EAAEmC,MAAM,CAACC,MAAM,CAAC9B,MAAM,EAAEN,KAAK,CAAC,CAAA;AAEvC,EAAA,OAAOM,MAAM,CAAA;AACf;;;;"}