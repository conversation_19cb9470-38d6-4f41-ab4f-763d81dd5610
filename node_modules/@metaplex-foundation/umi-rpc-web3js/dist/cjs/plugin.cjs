'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var createWeb3JsRpc = require('./createWeb3JsRpc.cjs');

function web3JsRpc(endpointOrConnection, rpcOptions) {
  return {
    install(umi) {
      umi.rpc = typeof endpointOrConnection === 'string' ? createWeb3JsRpc.createWeb3JsRpc(umi, endpointOrConnection, rpcOptions) : createWeb3JsRpc.createWeb3JsRpc(umi, endpointOrConnection);
    }
  };
}

exports.web3JsRpc = web3JsRpc;
//# sourceMappingURL=plugin.cjs.map
