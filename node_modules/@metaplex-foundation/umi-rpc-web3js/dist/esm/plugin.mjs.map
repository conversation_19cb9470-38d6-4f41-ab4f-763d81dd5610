{"version": 3, "file": "plugin.mjs", "sources": ["../../src/plugin.ts"], "sourcesContent": ["import { UmiPlugin } from '@metaplex-foundation/umi';\nimport type { Connection as Web3JsConnection } from '@solana/web3.js';\nimport { createWeb3JsRpc, Web3JsRpcOptions } from './createWeb3JsRpc';\n\nexport function web3JsRpc(\n  endpoint: string,\n  rpcOptions?: Web3JsRpcOptions\n): UmiPlugin;\nexport function web3JsRpc(connection: Web3JsConnection): UmiPlugin;\nexport function web3JsRpc(\n  endpointOrConnection: string | Web3JsConnection,\n  rpcOptions?: Web3JsRpcOptions\n): UmiPlugin {\n  return {\n    install(umi) {\n      umi.rpc =\n        typeof endpointOrConnection === 'string'\n          ? createWeb3JsRpc(umi, endpointOrConnection, rpcOptions)\n          : createWeb3JsRpc(umi, endpointOrConnection);\n    },\n  };\n}\n"], "names": ["web3JsRpc", "endpointOrConnection", "rpcOptions", "install", "umi", "rpc", "createWeb3JsRpc"], "mappings": ";;AASO,SAASA,SAAS,CACvBC,oBAA+C,EAC/CC,UAA6B,EAClB;EACX,OAAO;IACLC,OAAO,CAACC,GAAG,EAAE;MACXA,GAAG,CAACC,GAAG,GACL,OAAOJ,oBAAoB,KAAK,QAAQ,GACpCK,eAAe,CAACF,GAAG,EAAEH,oBAAoB,EAAEC,UAAU,CAAC,GACtDI,eAAe,CAACF,GAAG,EAAEH,oBAAoB,CAAC,CAAA;AAClD,KAAA;GACD,CAAA;AACH;;;;"}