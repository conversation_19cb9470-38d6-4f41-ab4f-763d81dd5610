import { Commitment, Context, RpcInterface } from '@metaplex-foundation/umi';
import { Connection as Web3JsConnection, ConnectionConfig as Web3JsConnectionConfig } from '@solana/web3.js';
export type Web3JsRpcOptions = Commitment | Web3JsConnectionConfig;
export declare function createWeb3JsRpc(context: Pick<Context, 'programs' | 'transactions'>, endpoint: string, rpcOptions?: Web3JsRpcOptions): RpcInterface & {
    connection: Web3JsConnection;
};
export declare function createWeb3JsRpc(context: Pick<Context, 'programs' | 'transactions'>, connection: Web3JsConnection): RpcInterface & {
    connection: Web3JsConnection;
};
