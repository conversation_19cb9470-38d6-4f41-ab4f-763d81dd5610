{"version": 3, "file": "createDataViewSerializer.cjs", "sources": ["../../src/createDataViewSerializer.ts"], "sourcesContent": ["import { SerializerInterface } from '@metaplex-foundation/umi';\nimport {\n  array,\n  bool,\n  bytes,\n  dataEnum,\n  f32,\n  f64,\n  i128,\n  i16,\n  i32,\n  i64,\n  i8,\n  map,\n  nullable,\n  option,\n  publicKey,\n  scalarEnum,\n  set,\n  string,\n  struct,\n  tuple,\n  u128,\n  u16,\n  u32,\n  u64,\n  u8,\n  unit,\n} from '@metaplex-foundation/umi/serializers';\n\nexport type DataViewSerializerOptions = {};\n\nexport function createDataViewSerializer(\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  options: DataViewSerializerOptions = {}\n): SerializerInterface {\n  return {\n    tuple,\n    array,\n    map,\n    set,\n    option,\n    nullable,\n    struct,\n    enum: scalarEnum,\n    dataEnum,\n    string,\n    bool,\n    unit,\n    u8,\n    u16,\n    u32,\n    u64,\n    u128,\n    i8,\n    i16,\n    i32,\n    i64,\n    i128,\n    f32,\n    f64,\n    bytes,\n    publicKey,\n  };\n}\n"], "names": ["createDataViewSerializer", "options", "tuple", "array", "map", "set", "option", "nullable", "struct", "enum", "scalarEnum", "dataEnum", "string", "bool", "unit", "u8", "u16", "u32", "u64", "u128", "i8", "i16", "i32", "i64", "i128", "f32", "f64", "bytes", "public<PERSON>ey"], "mappings": ";;;;;;AAgCO,SAASA,wBAAwB;AACtC;AACAC,OAAkC,GAAG,EAAE,EAClB;EACrB,OAAO;WACLC,iBAAK;WACLC,iBAAK;SACLC,eAAG;SACHC,eAAG;YACHC,kBAAM;cACNC,oBAAQ;YACRC,kBAAM;AACNC,IAAAA,IAAI,EAAEC,sBAAU;cAChBC,oBAAQ;YACRC,kBAAM;UACNC,gBAAI;UACJC,gBAAI;QACJC,cAAE;SACFC,eAAG;SACHC,eAAG;SACHC,eAAG;UACHC,gBAAI;QACJC,cAAE;SACFC,eAAG;SACHC,eAAG;SACHC,eAAG;UACHC,gBAAI;SACJC,eAAG;SACHC,eAAG;WACHC,iBAAK;AACLC,eAAAA,qBAAAA;GACD,CAAA;AACH;;;;"}