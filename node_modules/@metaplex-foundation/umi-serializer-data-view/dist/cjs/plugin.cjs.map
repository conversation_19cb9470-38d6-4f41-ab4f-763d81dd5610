{"version": 3, "file": "plugin.cjs", "sources": ["../../src/plugin.ts"], "sourcesContent": ["import { UmiPlugin } from '@metaplex-foundation/umi';\nimport {\n  DataViewSerializerOptions,\n  createDataViewSerializer,\n} from './createDataViewSerializer';\n\nexport const dataViewSerializer = (\n  options: DataViewSerializerOptions = {}\n): UmiPlugin => ({\n  install(umi) {\n    umi.serializer = createDataViewSerializer(options);\n  },\n});\n"], "names": ["dataViewSerializer", "options", "install", "umi", "serializer", "createDataViewSerializer"], "mappings": ";;;;;;AAMO,MAAMA,kBAAkB,GAAG,CAChCC,OAAkC,GAAG,EAAE,MACxB;EACfC,OAAO,CAACC,GAAG,EAAE;AACXA,IAAAA,GAAG,CAACC,UAAU,GAAGC,iDAAwB,CAACJ,OAAO,CAAC,CAAA;AACpD,GAAA;AACF,CAAC;;;;"}