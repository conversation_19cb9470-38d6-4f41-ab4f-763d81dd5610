'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var umi = require('@metaplex-foundation/umi');
var serializers = require('@metaplex-foundation/umi/serializers');
var umiWeb3jsAdapters = require('@metaplex-foundation/umi-web3js-adapters');
var web3_js = require('@solana/web3.js');

/* eslint-disable no-bitwise */
const TRANSACTION_VERSION_FLAG = 0x80;
const TRANSACTION_VERSION_MASK = 0x7f;
function createWeb3JsTransactionFactory() {
  const create = input => {
    const web3JsMessage = umiWeb3jsAdapters.toWeb3JsMessageFromInput(input);
    const message = umiWeb3jsAdapters.fromWeb3JsMessage(web3JsMessage);
    const web3JsTransaction = new web3_js.VersionedTransaction(web3JsMessage, input.signatures);
    return {
      message,
      serializedMessage: serializeMessage(message),
      signatures: web3JsTransaction.signatures
    };
  };
  const serialize = transaction => getTransactionSerializer().serialize(transaction);
  const deserialize = serializedTransaction => getTransactionSerializer().deserialize(serializedTransaction)[0];
  const serializeMessage = message => getTransactionMessageSerializer().serialize(message);
  const deserializeMessage = serializedMessage => getTransactionMessageSerializer().deserialize(serializedMessage)[0];
  const getTransactionSerializer = () => ({
    ...serializers.mapSerializer(serializers.struct([['signatures', serializers.array(serializers.bytes({
      size: 64
    }), {
      size: serializers.shortU16()
    })], ['serializedMessage', serializers.bytes()]]), value => value, value => ({
      ...value,
      message: deserializeMessage(value.serializedMessage)
    })),
    description: 'Transaction'
  });
  const getTransactionMessageSerializer = () => ({
    description: 'TransactionMessage',
    fixedSize: null,
    maxSize: null,
    serialize: value => {
      const serializer = getTransactionMessageSerializerForVersion(value.version);
      return serializer.serialize(value);
    },
    deserialize: (bytes, offset = 0) => {
      const [version] = getTransactionVersionSerializer().deserialize(bytes, offset);
      const serializer = getTransactionMessageSerializerForVersion(version);
      return serializer.deserialize(bytes, offset);
    }
  });
  const getTransactionMessageSerializerForVersion = version => serializers.struct([['version', getTransactionVersionSerializer()], ['header', getTransactionMessageHeaderSerializer()], ['accounts', serializers.array(serializers.publicKey(), {
    size: serializers.shortU16()
  })], ['blockhash', serializers.string({
    encoding: serializers.base58,
    size: 32
  })], ['instructions', serializers.array(getCompiledInstructionSerializer(), {
    size: serializers.shortU16()
  })], ['addressLookupTables', serializers.array(getCompiledAddressLookupTableSerializer(), {
    size: version === 'legacy' ? 0 : serializers.shortU16()
  })]]);
  const getTransactionVersionSerializer = () => ({
    description: 'TransactionVersion',
    fixedSize: null,
    maxSize: 1,
    serialize: value => {
      if (value === 'legacy') return new Uint8Array([]);
      return new Uint8Array([TRANSACTION_VERSION_FLAG | value]);
    },
    deserialize: (bytes, offset = 0) => {
      const slice = bytes.slice(offset);
      if (slice.length === 0 || (slice[0] & TRANSACTION_VERSION_FLAG) === 0) {
        return ['legacy', offset];
      }
      const version = slice[0] & TRANSACTION_VERSION_MASK;
      if (version > 0) {
        throw new umi.SdkError(`Unsupported transaction version: ${version}.`);
      }
      return [version, offset + 1];
    }
  });
  const getTransactionMessageHeaderSerializer = () => serializers.struct([['numRequiredSignatures', serializers.u8()], ['numReadonlySignedAccounts', serializers.u8()], ['numReadonlyUnsignedAccounts', serializers.u8()]]);
  const getCompiledInstructionSerializer = () => serializers.struct([['programIndex', serializers.u8()], ['accountIndexes', serializers.array(serializers.u8(), {
    size: serializers.shortU16()
  })], ['data', serializers.bytes({
    size: serializers.shortU16()
  })]]);
  const getCompiledAddressLookupTableSerializer = () => serializers.struct([['publicKey', serializers.publicKey()], ['writableIndexes', serializers.array(serializers.u8(), {
    size: serializers.shortU16()
  })], ['readonlyIndexes', serializers.array(serializers.u8(), {
    size: serializers.shortU16()
  })]]);
  return {
    create,
    serialize,
    deserialize,
    serializeMessage,
    deserializeMessage
  };
}

exports.createWeb3JsTransactionFactory = createWeb3JsTransactionFactory;
//# sourceMappingURL=createWeb3JsTransactionFactory.cjs.map
