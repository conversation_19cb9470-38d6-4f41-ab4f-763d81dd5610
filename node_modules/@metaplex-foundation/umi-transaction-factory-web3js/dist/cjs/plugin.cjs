'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var createWeb3JsTransactionFactory = require('./createWeb3JsTransactionFactory.cjs');

const web3JsTransactionFactory = () => ({
  install(umi) {
    umi.transactions = createWeb3JsTransactionFactory.createWeb3JsTransactionFactory();
  }
});

exports.web3JsTransactionFactory = web3JsTransactionFactory;
//# sourceMappingURL=plugin.cjs.map
