{"version": 3, "file": "plugin.cjs", "sources": ["../../src/plugin.ts"], "sourcesContent": ["import { UmiPlugin } from '@metaplex-foundation/umi';\nimport { createWeb3JsTransactionFactory } from './createWeb3JsTransactionFactory';\n\nexport const web3JsTransactionFactory = (): UmiPlugin => ({\n  install(umi) {\n    umi.transactions = createWeb3JsTransactionFactory();\n  },\n});\n"], "names": ["web3JsTransactionFactory", "install", "umi", "transactions", "createWeb3JsTransactionFactory"], "mappings": ";;;;;;AAGO,MAAMA,wBAAwB,GAAG,OAAkB;EACxDC,OAAO,CAACC,GAAG,EAAE;AACXA,IAAAA,GAAG,CAACC,YAAY,GAAGC,6DAA8B,EAAE,CAAA;AACrD,GAAA;AACF,CAAC;;;;"}