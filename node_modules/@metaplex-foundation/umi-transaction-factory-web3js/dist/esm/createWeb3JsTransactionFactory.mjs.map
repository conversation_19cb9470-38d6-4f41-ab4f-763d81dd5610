{"version": 3, "file": "createWeb3JsTransactionFactory.mjs", "sources": ["../../src/createWeb3JsTransactionFactory.ts"], "sourcesContent": ["/* eslint-disable no-bitwise */\nimport {\n  CompiledAddressLookupTable,\n  CompiledInstruction,\n  SdkError,\n  SerializedTransaction,\n  SerializedTransactionMessage,\n  Transaction,\n  TransactionFactoryInterface,\n  TransactionInput,\n  TransactionMessage,\n  TransactionMessageHeader,\n  TransactionVersion,\n} from '@metaplex-foundation/umi';\nimport {\n  shortU16,\n  base58,\n  Serializer,\n  mapSerializer,\n  struct,\n  bytes,\n  array,\n  string,\n  publicKey,\n  u8,\n} from '@metaplex-foundation/umi/serializers';\nimport {\n  fromWeb3JsMessage,\n  toWeb3JsMessageFromInput,\n} from '@metaplex-foundation/umi-web3js-adapters';\nimport { VersionedTransaction as Web3JsTransaction } from '@solana/web3.js';\n\nconst TRANSACTION_VERSION_FLAG = 0x80;\nconst TRANSACTION_VERSION_MASK = 0x7f;\n\nexport function createWeb3JsTransactionFactory(): TransactionFactoryInterface {\n  const create = (input: TransactionInput): Transaction => {\n    const web3JsMessage = toWeb3JsMessageFromInput(input);\n    const message = fromWeb3JsMessage(web3JsMessage);\n    const web3JsTransaction = new Web3JsTransaction(\n      web3JsMessage,\n      input.signatures\n    );\n    return {\n      message,\n      serializedMessage: serializeMessage(message),\n      signatures: web3JsTransaction.signatures,\n    };\n  };\n\n  const serialize = (transaction: Transaction): SerializedTransaction =>\n    getTransactionSerializer().serialize(transaction);\n\n  const deserialize = (\n    serializedTransaction: SerializedTransaction\n  ): Transaction =>\n    getTransactionSerializer().deserialize(serializedTransaction)[0];\n\n  const serializeMessage = (\n    message: TransactionMessage\n  ): SerializedTransactionMessage =>\n    getTransactionMessageSerializer().serialize(message);\n\n  const deserializeMessage = (\n    serializedMessage: SerializedTransactionMessage\n  ): TransactionMessage =>\n    getTransactionMessageSerializer().deserialize(serializedMessage)[0];\n\n  const getTransactionSerializer = (): Serializer<Transaction> => ({\n    ...mapSerializer(\n      struct<Omit<Transaction, 'message'>>([\n        ['signatures', array(bytes({ size: 64 }), { size: shortU16() })],\n        ['serializedMessage', bytes()],\n      ]),\n      (value: Transaction): Omit<Transaction, 'message'> => value,\n      (value: Omit<Transaction, 'message'>): Transaction => ({\n        ...value,\n        message: deserializeMessage(value.serializedMessage),\n      })\n    ),\n    description: 'Transaction',\n  });\n\n  const getTransactionMessageSerializer =\n    (): Serializer<TransactionMessage> => ({\n      description: 'TransactionMessage',\n      fixedSize: null,\n      maxSize: null,\n      serialize: (value: TransactionMessage): Uint8Array => {\n        const serializer = getTransactionMessageSerializerForVersion(\n          value.version\n        );\n        return serializer.serialize(value);\n      },\n      deserialize: (\n        bytes: Uint8Array,\n        offset = 0\n      ): [TransactionMessage, number] => {\n        const [version] = getTransactionVersionSerializer().deserialize(\n          bytes,\n          offset\n        );\n        const serializer = getTransactionMessageSerializerForVersion(version);\n        return serializer.deserialize(bytes, offset);\n      },\n    });\n\n  const getTransactionMessageSerializerForVersion = (\n    version: TransactionVersion\n  ): Serializer<TransactionMessage> =>\n    struct<TransactionMessage, TransactionMessage>([\n      ['version', getTransactionVersionSerializer()],\n      ['header', getTransactionMessageHeaderSerializer()],\n      ['accounts', array(publicKey(), { size: shortU16() })],\n      ['blockhash', string({ encoding: base58, size: 32 })],\n      [\n        'instructions',\n        array(getCompiledInstructionSerializer(), { size: shortU16() }),\n      ],\n      [\n        'addressLookupTables',\n        array(getCompiledAddressLookupTableSerializer(), {\n          size: version === 'legacy' ? 0 : shortU16(),\n        }),\n      ],\n    ]);\n\n  const getTransactionVersionSerializer =\n    (): Serializer<TransactionVersion> => ({\n      description: 'TransactionVersion',\n      fixedSize: null,\n      maxSize: 1,\n      serialize: (value: TransactionVersion): Uint8Array => {\n        if (value === 'legacy') return new Uint8Array([]);\n        return new Uint8Array([TRANSACTION_VERSION_FLAG | value]);\n      },\n      deserialize: (\n        bytes: Uint8Array,\n        offset = 0\n      ): [TransactionVersion, number] => {\n        const slice = bytes.slice(offset);\n        if (slice.length === 0 || (slice[0] & TRANSACTION_VERSION_FLAG) === 0) {\n          return ['legacy', offset];\n        }\n        const version = slice[0] & TRANSACTION_VERSION_MASK;\n        if (version > 0) {\n          throw new SdkError(`Unsupported transaction version: ${version}.`);\n        }\n        return [version as TransactionVersion, offset + 1];\n      },\n    });\n\n  const getTransactionMessageHeaderSerializer =\n    (): Serializer<TransactionMessageHeader> =>\n      struct([\n        ['numRequiredSignatures', u8()],\n        ['numReadonlySignedAccounts', u8()],\n        ['numReadonlyUnsignedAccounts', u8()],\n      ]);\n\n  const getCompiledInstructionSerializer =\n    (): Serializer<CompiledInstruction> =>\n      struct([\n        ['programIndex', u8()],\n        ['accountIndexes', array(u8(), { size: shortU16() })],\n        ['data', bytes({ size: shortU16() })],\n      ]);\n\n  const getCompiledAddressLookupTableSerializer =\n    (): Serializer<CompiledAddressLookupTable> =>\n      struct([\n        ['publicKey', publicKey()],\n        ['writableIndexes', array(u8(), { size: shortU16() })],\n        ['readonlyIndexes', array(u8(), { size: shortU16() })],\n      ]);\n\n  return {\n    create,\n    serialize,\n    deserialize,\n    serializeMessage,\n    deserializeMessage,\n  };\n}\n"], "names": ["TRANSACTION_VERSION_FLAG", "TRANSACTION_VERSION_MASK", "createWeb3JsTransactionFactory", "create", "input", "web3JsMessage", "toWeb3JsMessageFromInput", "message", "fromWeb3JsMessage", "web3JsTransaction", "Web3JsTransaction", "signatures", "serializedMessage", "serializeMessage", "serialize", "transaction", "getTransactionSerializer", "deserialize", "serializedTransaction", "getTransactionMessageSerializer", "deserializeMessage", "mapSerializer", "struct", "array", "bytes", "size", "shortU16", "value", "description", "fixedSize", "maxSize", "serializer", "getTransactionMessageSerializerForVersion", "version", "offset", "getTransactionVersionSerializer", "getTransactionMessageHeaderSerializer", "public<PERSON>ey", "string", "encoding", "base58", "getCompiledInstructionSerializer", "getCompiledAddressLookupTableSerializer", "Uint8Array", "slice", "length", "SdkError", "u8"], "mappings": ";;;;;AAAA;AAgCA,MAAMA,wBAAwB,GAAG,IAAI,CAAA;AACrC,MAAMC,wBAAwB,GAAG,IAAI,CAAA;AAE9B,SAASC,8BAA8B,GAAgC;EAC5E,MAAMC,MAAM,GAAIC,KAAuB,IAAkB;AACvD,IAAA,MAAMC,aAAa,GAAGC,wBAAwB,CAACF,KAAK,CAAC,CAAA;AACrD,IAAA,MAAMG,OAAO,GAAGC,iBAAiB,CAACH,aAAa,CAAC,CAAA;IAChD,MAAMI,iBAAiB,GAAG,IAAIC,oBAAiB,CAC7CL,aAAa,EACbD,KAAK,CAACO,UAAU,CACjB,CAAA;IACD,OAAO;MACLJ,OAAO;AACPK,MAAAA,iBAAiB,EAAEC,gBAAgB,CAACN,OAAO,CAAC;MAC5CI,UAAU,EAAEF,iBAAiB,CAACE,UAAAA;KAC/B,CAAA;GACF,CAAA;EAED,MAAMG,SAAS,GAAIC,WAAwB,IACzCC,wBAAwB,EAAE,CAACF,SAAS,CAACC,WAAW,CAAC,CAAA;AAEnD,EAAA,MAAME,WAAW,GACfC,qBAA4C,IAE5CF,wBAAwB,EAAE,CAACC,WAAW,CAACC,qBAAqB,CAAC,CAAC,CAAC,CAAC,CAAA;EAElE,MAAML,gBAAgB,GACpBN,OAA2B,IAE3BY,+BAA+B,EAAE,CAACL,SAAS,CAACP,OAAO,CAAC,CAAA;AAEtD,EAAA,MAAMa,kBAAkB,GACtBR,iBAA+C,IAE/CO,+BAA+B,EAAE,CAACF,WAAW,CAACL,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAA;EAErE,MAAMI,wBAAwB,GAAG,OAAgC;IAC/D,GAAGK,aAAa,CACdC,MAAM,CAA+B,CACnC,CAAC,YAAY,EAAEC,KAAK,CAACC,KAAK,CAAC;AAAEC,MAAAA,IAAI,EAAE,EAAA;AAAG,KAAC,CAAC,EAAE;AAAEA,MAAAA,IAAI,EAAEC,QAAQ,EAAA;AAAG,KAAC,CAAC,CAAC,EAChE,CAAC,mBAAmB,EAAEF,KAAK,EAAE,CAAC,CAC/B,CAAC,EACDG,KAAkB,IAAmCA,KAAK,EAC1DA,KAAmC,KAAmB;AACrD,MAAA,GAAGA,KAAK;AACRpB,MAAAA,OAAO,EAAEa,kBAAkB,CAACO,KAAK,CAACf,iBAAiB,CAAA;AACrD,KAAC,CAAC,CACH;AACDgB,IAAAA,WAAW,EAAE,aAAA;AACf,GAAC,CAAC,CAAA;EAEF,MAAMT,+BAA+B,GACnC,OAAuC;AACrCS,IAAAA,WAAW,EAAE,oBAAoB;AACjCC,IAAAA,SAAS,EAAE,IAAI;AACfC,IAAAA,OAAO,EAAE,IAAI;IACbhB,SAAS,EAAGa,KAAyB,IAAiB;AACpD,MAAA,MAAMI,UAAU,GAAGC,yCAAyC,CAC1DL,KAAK,CAACM,OAAO,CACd,CAAA;AACD,MAAA,OAAOF,UAAU,CAACjB,SAAS,CAACa,KAAK,CAAC,CAAA;KACnC;AACDV,IAAAA,WAAW,EAAE,CACXO,KAAiB,EACjBU,MAAM,GAAG,CAAC,KACuB;AACjC,MAAA,MAAM,CAACD,OAAO,CAAC,GAAGE,+BAA+B,EAAE,CAAClB,WAAW,CAC7DO,KAAK,EACLU,MAAM,CACP,CAAA;AACD,MAAA,MAAMH,UAAU,GAAGC,yCAAyC,CAACC,OAAO,CAAC,CAAA;AACrE,MAAA,OAAOF,UAAU,CAACd,WAAW,CAACO,KAAK,EAAEU,MAAM,CAAC,CAAA;AAC9C,KAAA;AACF,GAAC,CAAC,CAAA;AAEJ,EAAA,MAAMF,yCAAyC,GAC7CC,OAA2B,IAE3BX,MAAM,CAAyC,CAC7C,CAAC,SAAS,EAAEa,+BAA+B,EAAE,CAAC,EAC9C,CAAC,QAAQ,EAAEC,qCAAqC,EAAE,CAAC,EACnD,CAAC,UAAU,EAAEb,KAAK,CAACc,SAAS,EAAE,EAAE;AAAEZ,IAAAA,IAAI,EAAEC,QAAQ,EAAA;AAAG,GAAC,CAAC,CAAC,EACtD,CAAC,WAAW,EAAEY,MAAM,CAAC;AAAEC,IAAAA,QAAQ,EAAEC,MAAM;AAAEf,IAAAA,IAAI,EAAE,EAAA;GAAI,CAAC,CAAC,EACrD,CACE,cAAc,EACdF,KAAK,CAACkB,gCAAgC,EAAE,EAAE;AAAEhB,IAAAA,IAAI,EAAEC,QAAQ,EAAA;GAAI,CAAC,CAChE,EACD,CACE,qBAAqB,EACrBH,KAAK,CAACmB,uCAAuC,EAAE,EAAE;AAC/CjB,IAAAA,IAAI,EAAEQ,OAAO,KAAK,QAAQ,GAAG,CAAC,GAAGP,QAAQ,EAAA;GAC1C,CAAC,CACH,CACF,CAAC,CAAA;EAEJ,MAAMS,+BAA+B,GACnC,OAAuC;AACrCP,IAAAA,WAAW,EAAE,oBAAoB;AACjCC,IAAAA,SAAS,EAAE,IAAI;AACfC,IAAAA,OAAO,EAAE,CAAC;IACVhB,SAAS,EAAGa,KAAyB,IAAiB;MACpD,IAAIA,KAAK,KAAK,QAAQ,EAAE,OAAO,IAAIgB,UAAU,CAAC,EAAE,CAAC,CAAA;MACjD,OAAO,IAAIA,UAAU,CAAC,CAAC3C,wBAAwB,GAAG2B,KAAK,CAAC,CAAC,CAAA;KAC1D;AACDV,IAAAA,WAAW,EAAE,CACXO,KAAiB,EACjBU,MAAM,GAAG,CAAC,KACuB;AACjC,MAAA,MAAMU,KAAK,GAAGpB,KAAK,CAACoB,KAAK,CAACV,MAAM,CAAC,CAAA;AACjC,MAAA,IAAIU,KAAK,CAACC,MAAM,KAAK,CAAC,IAAI,CAACD,KAAK,CAAC,CAAC,CAAC,GAAG5C,wBAAwB,MAAM,CAAC,EAAE;AACrE,QAAA,OAAO,CAAC,QAAQ,EAAEkC,MAAM,CAAC,CAAA;AAC3B,OAAA;AACA,MAAA,MAAMD,OAAO,GAAGW,KAAK,CAAC,CAAC,CAAC,GAAG3C,wBAAwB,CAAA;MACnD,IAAIgC,OAAO,GAAG,CAAC,EAAE;AACf,QAAA,MAAM,IAAIa,QAAQ,CAAE,CAAmCb,iCAAAA,EAAAA,OAAQ,GAAE,CAAC,CAAA;AACpE,OAAA;AACA,MAAA,OAAO,CAACA,OAAO,EAAwBC,MAAM,GAAG,CAAC,CAAC,CAAA;AACpD,KAAA;AACF,GAAC,CAAC,CAAA;EAEJ,MAAME,qCAAqC,GACzC,MACEd,MAAM,CAAC,CACL,CAAC,uBAAuB,EAAEyB,EAAE,EAAE,CAAC,EAC/B,CAAC,2BAA2B,EAAEA,EAAE,EAAE,CAAC,EACnC,CAAC,6BAA6B,EAAEA,EAAE,EAAE,CAAC,CACtC,CAAC,CAAA;EAEN,MAAMN,gCAAgC,GACpC,MACEnB,MAAM,CAAC,CACL,CAAC,cAAc,EAAEyB,EAAE,EAAE,CAAC,EACtB,CAAC,gBAAgB,EAAExB,KAAK,CAACwB,EAAE,EAAE,EAAE;AAAEtB,IAAAA,IAAI,EAAEC,QAAQ,EAAA;AAAG,GAAC,CAAC,CAAC,EACrD,CAAC,MAAM,EAAEF,KAAK,CAAC;AAAEC,IAAAA,IAAI,EAAEC,QAAQ,EAAA;GAAI,CAAC,CAAC,CACtC,CAAC,CAAA;EAEN,MAAMgB,uCAAuC,GAC3C,MACEpB,MAAM,CAAC,CACL,CAAC,WAAW,EAAEe,SAAS,EAAE,CAAC,EAC1B,CAAC,iBAAiB,EAAEd,KAAK,CAACwB,EAAE,EAAE,EAAE;AAAEtB,IAAAA,IAAI,EAAEC,QAAQ,EAAA;GAAI,CAAC,CAAC,EACtD,CAAC,iBAAiB,EAAEH,KAAK,CAACwB,EAAE,EAAE,EAAE;AAAEtB,IAAAA,IAAI,EAAEC,QAAQ,EAAA;GAAI,CAAC,CAAC,CACvD,CAAC,CAAA;EAEN,OAAO;IACLvB,MAAM;IACNW,SAAS;IACTG,WAAW;IACXJ,gBAAgB;AAChBO,IAAAA,kBAAAA;GACD,CAAA;AACH;;;;"}