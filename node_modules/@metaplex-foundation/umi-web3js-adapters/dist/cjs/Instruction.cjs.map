{"version": 3, "file": "Instruction.cjs", "sources": ["../../src/Instruction.ts"], "sourcesContent": ["import { Instruction } from '@metaplex-foundation/umi';\nimport { TransactionInstruction as Web3JsTransactionInstruction } from '@solana/web3.js';\nimport { <PERSON><PERSON><PERSON> } from 'buffer';\nimport { fromWeb3JsPublicKey, toWeb3JsPublicKey } from './PublicKey';\n\nexport function fromWeb3JsInstruction(\n  instruction: Web3JsTransactionInstruction\n): Instruction {\n  return {\n    keys: instruction.keys.map((accountMeta) => ({\n      ...accountMeta,\n      pubkey: fromWeb3JsPublicKey(accountMeta.pubkey),\n    })),\n    programId: fromWeb3JsPublicKey(instruction.programId),\n    data: new Uint8Array(instruction.data),\n  };\n}\n\nexport function toWeb3JsInstruction(\n  instruction: Instruction\n): Web3JsTransactionInstruction {\n  return new Web3JsTransactionInstruction({\n    keys: instruction.keys.map((accountMeta) => ({\n      ...accountMeta,\n      pubkey: toWeb3Js<PERSON><PERSON><PERSON><PERSON><PERSON>(accountMeta.pubkey),\n    })),\n    programId: toWeb3<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(instruction.programId),\n    data: Buffer.from(instruction.data),\n  });\n}\n"], "names": ["fromWeb3JsInstruction", "instruction", "keys", "map", "accountMeta", "pubkey", "fromWeb3JsPublicKey", "programId", "data", "Uint8Array", "toWeb3JsInstruction", "Web3JsTransactionInstruction", "toWeb3JsPublicKey", "<PERSON><PERSON><PERSON>", "from"], "mappings": ";;;;;;;;AAKO,SAASA,qBAAqB,CACnCC,WAAyC,EAC5B;EACb,OAAO;IACLC,IAAI,EAAED,WAAW,CAACC,IAAI,CAACC,GAAG,CAAEC,WAAW,KAAM;AAC3C,MAAA,GAAGA,WAAW;AACdC,MAAAA,MAAM,EAAEC,6BAAmB,CAACF,WAAW,CAACC,MAAM,CAAA;AAChD,KAAC,CAAC,CAAC;AACHE,IAAAA,SAAS,EAAED,6BAAmB,CAACL,WAAW,CAACM,SAAS,CAAC;AACrDC,IAAAA,IAAI,EAAE,IAAIC,UAAU,CAACR,WAAW,CAACO,IAAI,CAAA;GACtC,CAAA;AACH,CAAA;AAEO,SAASE,mBAAmB,CACjCT,WAAwB,EACM;EAC9B,OAAO,IAAIU,8BAA4B,CAAC;IACtCT,IAAI,EAAED,WAAW,CAACC,IAAI,CAACC,GAAG,CAAEC,WAAW,KAAM;AAC3C,MAAA,GAAGA,WAAW;AACdC,MAAAA,MAAM,EAAEO,2BAAiB,CAACR,WAAW,CAACC,MAAM,CAAA;AAC9C,KAAC,CAAC,CAAC;AACHE,IAAAA,SAAS,EAAEK,2BAAiB,CAACX,WAAW,CAACM,SAAS,CAAC;AACnDC,IAAAA,IAAI,EAAEK,aAAM,CAACC,IAAI,CAACb,WAAW,CAACO,IAAI,CAAA;AACpC,GAAC,CAAC,CAAA;AACJ;;;;;"}