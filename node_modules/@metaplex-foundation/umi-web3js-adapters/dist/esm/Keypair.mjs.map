{"version": 3, "file": "Keypair.mjs", "sources": ["../../src/Keypair.ts"], "sourcesContent": ["import { Keypair, publicKeyBytes } from '@metaplex-foundation/umi';\nimport { Keypair as Web3JsKeypair } from '@solana/web3.js';\nimport { fromWeb3JsPublicKey } from './PublicKey';\n\nexport function fromWeb3JsKeypair(keypair: Web3JsKeypair): Keypair {\n  return {\n    publicKey: fromWeb3Js<PERSON><PERSON><PERSON>Key(keypair.publicKey),\n    secretKey: keypair.secretKey,\n  };\n}\n\nexport function toWeb3JsKeypair(keypair: Keypair): Web3JsKeypair {\n  return new Web3JsKeypair({\n    publicKey: publicKeyBytes(keypair.publicKey),\n    secretKey: keypair.secretKey,\n  });\n}\n"], "names": ["fromWeb3JsKeypair", "keypair", "public<PERSON>ey", "fromWeb3JsPublicKey", "secret<PERSON>ey", "toWeb3JsKeypair", "Web3JsKeypair", "publicKeyBytes"], "mappings": ";;;;AAIO,SAASA,iBAAiB,CAACC,OAAsB,EAAW;EACjE,OAAO;AACLC,IAAAA,SAAS,EAAEC,mBAAmB,CAACF,OAAO,CAACC,SAAS,CAAC;IACjDE,SAAS,EAAEH,OAAO,CAACG,SAAAA;GACpB,CAAA;AACH,CAAA;AAEO,SAASC,eAAe,CAACJ,OAAgB,EAAiB;EAC/D,OAAO,IAAIK,OAAa,CAAC;AACvBJ,IAAAA,SAAS,EAAEK,cAAc,CAACN,OAAO,CAACC,SAAS,CAAC;IAC5CE,SAAS,EAAEH,OAAO,CAACG,SAAAA;AACrB,GAAC,CAAC,CAAA;AACJ;;;;"}