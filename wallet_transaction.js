require('dotenv').config();
const { Connection, PublicKey, clusterApiUrl, LAMPORTS_PER_SOL } = require('@solana/web3.js');

class OptimizedWalletTradeAnalyzer {
    constructor() {
        this.walletAddress = process.env.WALLET_ADDRESS;
        this.rpcUrl = process.env.SOLANA_RPC_URL || clusterApiUrl('mainnet-beta');
        this.connection = new Connection(this.rpcUrl, 'confirmed');

        // Cache for token metadata
        this.tokenMetadataCache = new Map();

        if (!this.walletAddress) {
            throw new Error('WALLET_ADDRESS not found in .env file');
        }

        try {
            this.publicKey = new PublicKey(this.walletAddress);
        } catch (error) {
            throw new Error(`Invalid wallet address format: ${this.walletAddress}`);
        }

        console.log(`🔗 Connected to: ${this.rpcUrl}`);
        console.log(`👛 Wallet Address: ${this.walletAddress}`);
    }

    /**
     * Fetch token metadata using a simple approach
     */
    async getTokenMetadata(mintAddress) {
        // Check cache first
        if (this.tokenMetadataCache.has(mintAddress)) {
            return this.tokenMetadataCache.get(mintAddress);
        }

        try {
            // Handle SOL specially
            if (mintAddress === 'So11111111111111111111111111111111111111112') {
                const metadata = {
                    mint: mintAddress,
                    symbol: 'SOL',
                    name: 'Solana',
                    decimals: 9
                };
                this.tokenMetadataCache.set(mintAddress, metadata);
                return metadata;
            }

            // Try to get basic mint info from on-chain data
            const mintPublicKey = new PublicKey(mintAddress);
            const mintInfo = await this.connection.getAccountInfo(mintPublicKey);

            let decimals = 0;
            if (mintInfo && mintInfo.data.length >= 44) {
                decimals = mintInfo.data[44]; // Mint decimals are at offset 44
            }

            // For now, we'll use a simplified approach
            // In a production environment, you might want to integrate with a token list API
            const metadata = {
                mint: mintAddress,
                symbol: this.getTokenSymbolFromMint(mintAddress),
                name: this.getTokenNameFromMint(mintAddress),
                decimals: decimals
            };

            this.tokenMetadataCache.set(mintAddress, metadata);
            return metadata;

        } catch (error) {
            // Fallback metadata
            const fallbackMetadata = {
                mint: mintAddress,
                symbol: 'UNKNOWN',
                name: 'Unknown Token',
                decimals: 0
            };
            this.tokenMetadataCache.set(mintAddress, fallbackMetadata);
            return fallbackMetadata;
        }
    }

    /**
     * Simple token symbol mapping for common tokens
     */
    getTokenSymbolFromMint(mintAddress) {
        const knownTokens = {
            'So11111111111111111111111111111111111111112': 'SOL',
            'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v': 'USDC',
            'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB': 'USDT',
            // Add more known tokens as needed
        };

        return knownTokens[mintAddress] || mintAddress.substring(0, 8) + '...';
    }

    /**
     * Simple token name mapping for common tokens
     */
    getTokenNameFromMint(mintAddress) {
        const knownTokens = {
            'So11111111111111111111111111111111111111112': 'Solana',
            'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v': 'USD Coin',
            'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB': 'Tether USD',
            // Add more known tokens as needed
        };

        return knownTokens[mintAddress] || `Token ${mintAddress.substring(0, 8)}...`;
    }

    /**
     * Parse token transfers from transaction to identify buy/sell tokens
     */

    async parseTokenTransfers(transaction) {
        const transfers = [];

        if (!transaction.meta || !transaction.meta.preTokenBalances || !transaction.meta.postTokenBalances) {
            return transfers;
        }

        const preBalances = transaction.meta.preTokenBalances;
        const postBalances = transaction.meta.postTokenBalances;

        // Group by account and mint
        const balanceChanges = new Map();

        // Process pre-balances
        for (const balance of preBalances) {
            const key = `${balance.accountIndex}-${balance.mint}`;
            if (!balanceChanges.has(key)) {
                balanceChanges.set(key, {
                    accountIndex: balance.accountIndex,
                    mint: balance.mint,
                    owner: balance.owner,
                    preAmount: parseFloat(balance.uiTokenAmount.amount),
                    postAmount: 0,
                    decimals: balance.uiTokenAmount.decimals
                });
            } else {
                balanceChanges.get(key).preAmount = parseFloat(balance.uiTokenAmount.amount);
            }
        }

        // Process post-balances
        for (const balance of postBalances) {
            const key = `${balance.accountIndex}-${balance.mint}`;
            if (!balanceChanges.has(key)) {
                balanceChanges.set(key, {
                    accountIndex: balance.accountIndex,
                    mint: balance.mint,
                    owner: balance.owner,
                    preAmount: 0,
                    postAmount: parseFloat(balance.uiTokenAmount.amount),
                    decimals: balance.uiTokenAmount.decimals
                });
            } else {
                balanceChanges.get(key).postAmount = parseFloat(balance.uiTokenAmount.amount);
            }
        }

        // Calculate changes for ALL accounts and identify counterparties
        for (const [, change] of balanceChanges) {
            const amount = change.postAmount - change.preAmount;
            if (amount !== 0) {
                const transfer = {
                    mint: change.mint,
                    amount: amount / Math.pow(10, change.decimals),
                    rawAmount: amount,
                    decimals: change.decimals,
                    type: amount > 0 ? 'received' : 'sent',
                    owner: change.owner,
                    isOwnerWallet: change.owner === this.walletAddress,
                    accountIndex: change.accountIndex
                };

                transfers.push(transfer);
            }
        }

        return transfers;
    }

    async parseSolTransfer(transaction) {
        if (!transaction.meta || !transaction.meta.preBalances || !transaction.meta.postBalances) {
            return null;
        }

        if (!transaction.transaction || !transaction.transaction.message || !transaction.transaction.message.accountKeys) {
            return null;
        }

        const accountKeys = transaction.transaction.message.accountKeys;
        let walletIndex = -1;

        // Find our wallet's index in the account keys
        for (let i = 0; i < accountKeys.length; i++) {
            if (accountKeys[i].toString() === this.walletAddress) {
                walletIndex = i;
                break;
            }
        }

        if (walletIndex === -1) return null;

        const preBalance = transaction.meta.preBalances[walletIndex];
        const postBalance = transaction.meta.postBalances[walletIndex];
        const change = (postBalance - preBalance) / LAMPORTS_PER_SOL;

        if (change === 0) return null;

        return {
            amount: Math.abs(change),
            type: change > 0 ? 'received' : 'sent',
            mint: 'So11111111111111111111111111111111111111112' // SOL mint address
        };
    }

    async getTransactionDetails(signature) {
        try {
            const transaction = await this.connection.getTransaction(signature, {
                maxSupportedTransactionVersion: 0
            });

            if (!transaction) {
                return null;
            }

            const tokenTransfers = await this.parseTokenTransfers(transaction);
            const solTransfer = await this.parseSolTransfer(transaction);

            // Extract signer information
            let signer = null;
            if (transaction.transaction && transaction.transaction.message) {
                try {
                    // Handle both legacy and versioned transactions
                    if ('accountKeys' in transaction.transaction.message) {
                        // Legacy transaction
                        if (transaction.transaction.message.accountKeys && transaction.transaction.message.accountKeys.length > 0) {
                            signer = transaction.transaction.message.accountKeys[0].toString();
                        }
                    } else if ('getAccountKeys' in transaction.transaction.message) {
                        // Versioned transaction
                        const accountKeys = transaction.transaction.message.getAccountKeys();
                        if (accountKeys && accountKeys.length > 0) {
                            signer = accountKeys.get(0)?.toString();
                        }
                    }

                    // Fallback: try to get from meta.postTokenBalances or preTokenBalances
                    if (!signer && transaction.meta) {
                        // Look for our wallet address in the transaction
                        if (transaction.meta.postTokenBalances) {
                            for (const balance of transaction.meta.postTokenBalances) {
                                if (balance.owner === this.walletAddress) {
                                    signer = this.walletAddress;
                                    break;
                                }
                            }
                        }
                    }
                } catch (error) {
                    // If we can't determine the signer, we'll leave it as null
                    console.log(`   ⚠️  Could not determine signer for transaction: ${error.message}`);
                }
            }

            return {
                signature,
                blockTime: transaction.blockTime,
                success: transaction.meta?.err === null,
                signer,
                tokenTransfers,
                solTransfer,
                logMessages: transaction.meta?.logMessages || [],
                instructions: transaction.transaction?.message?.instructions?.length || 0
            };

        } catch (error) {
            console.error(`❌ Error fetching transaction ${signature}:`, error.message);
            return null;
        }
    }

    async getLastTransactions(count = 10) {
        console.log(`\n🚀 Fetching last ${count} transactions...`);

        const signatures = await this.connection.getSignaturesForAddress(
            this.publicKey,
            { limit: count }
        );

        if (signatures.length === 0) {
            console.log('📭 No transactions found for this wallet.');
            return [];
        }

        const transactions = [];

        for (let i = 0; i < signatures.length; i++) {
            const signatureInfo = signatures[i];
            const details = await this.getTransactionDetails(signatureInfo.signature);

            if (details) {
                transactions.push(details);
            }
        }

        return transactions;
    }

    async getLatestSignedTransactions(targetCount = 30) {
        const startTime = Date.now();
        console.log(`🚀 Fetching latest ${targetCount} signed transactions (optimized)...`);
        console.log(`⏱️  Start time: ${new Date(startTime).toLocaleTimeString()}`);

        let signedTransactions = [];
        let before = null;
        let batchCount = 0;
        let totalProcessed = 0;
        const maxBatches = 50; // Safety limit
        const batchSize = 50; // Smaller batches to reduce rate limiting

        while (batchCount < maxBatches && signedTransactions.length < targetCount) {
            batchCount++;
            console.log(`   📦 Batch ${batchCount}: Looking for signed transactions...`);

            const options = { limit: batchSize };
            if (before) {
                options.before = before;
            }

            const signatures = await this.connection.getSignaturesForAddress(
                this.publicKey,
                options
            );

            if (signatures.length === 0) {
                console.log(`   ✅ No more signatures found. Total batches: ${batchCount - 1}`);
                break;
            }

            // Process signatures sequentially with rate limiting to avoid 429 errors
            for (let i = 0; i < signatures.length; i++) {
                if (signedTransactions.length >= targetCount) break;

                const signatureInfo = signatures[i];
                try {
                    // Quick check: Get basic transaction info first
                    const transaction = await this.connection.getTransaction(signatureInfo.signature, {
                        maxSupportedTransactionVersion: 0,
                        commitment: 'confirmed'
                    });

                    if (!transaction) {
                        totalProcessed++;
                        continue;
                    }

                    // Quick signer check before full processing
                    let signer = null;
                    if (transaction.transaction && transaction.transaction.message) {
                        if ('accountKeys' in transaction.transaction.message) {
                            if (transaction.transaction.message.accountKeys && transaction.transaction.message.accountKeys.length > 0) {
                                signer = transaction.transaction.message.accountKeys[0].toString();
                            }
                        } else if ('getAccountKeys' in transaction.transaction.message) {
                            const accountKeys = transaction.transaction.message.getAccountKeys();
                            if (accountKeys && accountKeys.length > 0) {
                                signer = accountKeys.get(0)?.toString();
                            }
                        }
                    }

                    totalProcessed++;

                    // Only process if this wallet is the signer
                    if (signer === this.walletAddress) {
                        // We already have the transaction, so let's process it directly
                        const tokenTransfers = await this.parseTokenTransfers(transaction);
                        const solTransfer = await this.parseSolTransfer(transaction);

                        const transactionDetails = {
                            signature: signatureInfo.signature,
                            blockTime: transaction.blockTime,
                            success: transaction.meta?.err === null,
                            signer,
                            tokenTransfers,
                            solTransfer,
                            logMessages: transaction.meta?.logMessages || [],
                            instructions: transaction.transaction?.message?.instructions?.length || 0
                        };

                        // Check if it's actually a trade
                        const tradeDetails = this.analyzeTradeDetails(tokenTransfers, solTransfer);
                        if (tradeDetails && tradeDetails.tradeType &&
                            ['BUY', 'SELL', 'SWAP'].includes(tradeDetails.tradeType)) {
                            signedTransactions.push(transactionDetails);
                        }
                    }

                    // Rate limiting: small delay every few transactions
                    if (i % 5 === 0) {
                        await new Promise(resolve => setTimeout(resolve, 100));
                    }

                } catch (error) {
                    console.log(`   ⚠️  Error checking transaction ${signatureInfo.signature}: ${error.message}`);
                    totalProcessed++;
                }

                // Progress update every 10 transactions
                if (totalProcessed % 10 === 0) {
                    console.log(`   🔄 Processed ${totalProcessed} transactions, found ${signedTransactions.length}/${targetCount} signed trades`);
                }
            }

            // Set the 'before' parameter to the last signature for pagination
            before = signatures[signatures.length - 1].signature;

            // If we got less than batchSize, we've reached the end
            if (signatures.length < batchSize) {
                console.log(`   ✅ Reached end of transaction history.`);
                break;
            }

            // Delay between batches to avoid rate limiting
            await new Promise(resolve => setTimeout(resolve, 200));
        }

        if (batchCount >= maxBatches) {
            console.log(`   ⚠️  Reached maximum batch limit (${maxBatches}).`);
        }

        const endTime = Date.now();
        const totalTime = endTime - startTime;
        const timeInSeconds = (totalTime / 1000).toFixed(2);
        const avgTimePerTransaction = totalProcessed > 0 ? (totalTime / totalProcessed).toFixed(0) : 0;

        console.log(`✅ Found ${signedTransactions.length} signed trade transactions out of ${totalProcessed} processed!`);
        console.log(`⏱️  Total time: ${timeInSeconds} seconds`);
        console.log(`⚡ Average time per transaction: ${avgTimePerTransaction}ms`);
        console.log(`🎯 Success rate: ${totalProcessed > 0 ? ((signedTransactions.length / totalProcessed) * 100).toFixed(1) : 0}% (${signedTransactions.length}/${totalProcessed})`);

        return signedTransactions;
    }

    async getAllTransactions() {
        console.log(`🚀 Fetching ALL transaction signatures...`);

        let allSignatures = [];
        let before = null;
        let batchCount = 0;
        const maxBatches = 100; // Safety limit to prevent infinite loops

        while (batchCount < maxBatches) {
            batchCount++;
            console.log(`   📦 Fetching batch ${batchCount}...`);

            const options = { limit: 1000 };
            if (before) {
                options.before = before;
            }

            const signatures = await this.connection.getSignaturesForAddress(
                this.publicKey,
                options
            );

            if (signatures.length === 0) {
                console.log(`   ✅ No more signatures found. Total batches: ${batchCount - 1}`);
                break;
            }

            allSignatures = allSignatures.concat(signatures);
            console.log(`   📊 Batch ${batchCount}: ${signatures.length} signatures (Total: ${allSignatures.length})`);

            // Set the 'before' parameter to the last signature for pagination
            before = signatures[signatures.length - 1].signature;

            // If we got less than 1000, we've reached the end
            if (signatures.length < 1000) {
                console.log(`   ✅ Reached end of transaction history. Total signatures: ${allSignatures.length}`);
                break;
            }

            // Small delay to avoid rate limiting
            await new Promise(resolve => setTimeout(resolve, 100));
        }

        if (batchCount >= maxBatches) {
            console.log(`   ⚠️  Reached maximum batch limit (${maxBatches}). Total signatures: ${allSignatures.length}`);
        }

        console.log(`\n💎 Processing ${allSignatures.length} transaction details...`);
        const transactions = [];

        for (let i = 0; i < allSignatures.length; i++) {
            if (i % 100 === 0) {
                console.log(`   🔄 Processing ${i + 1}/${allSignatures.length} transactions...`);
            }

            const signatureInfo = allSignatures[i];
            const details = await this.getTransactionDetails(signatureInfo.signature);

            if (details) {
                transactions.push(details);
            }

            // Small delay to avoid rate limiting
            if (i % 10 === 0) {
                await new Promise(resolve => setTimeout(resolve, 50));
            }
        }

        console.log(`✅ Processed ${transactions.length} transaction details successfully!`);
        return transactions;
    }

    /**
     * Analyze trade and extract buy/sell information with enhanced detection and counterparty identification
     */
    analyzeTradeDetails(tokenTransfers, solTransfer) {
        if (!tokenTransfers || tokenTransfers.length === 0) {
            // Check if there's only SOL movement (might be wrapped SOL operations)
            if (solTransfer && Math.abs(solTransfer.amount) > 0.000001) {
                return {
                    tradeType: solTransfer.type === 'received' ? 'SOL_RECEIVE' : 'SOL_SEND',
                    buyTokenMint: solTransfer.type === 'received' ? 'So11111111111111111111111111111111111111112' : null,
                    sellTokenMint: solTransfer.type === 'sent' ? 'So11111111111111111111111111111111111111112' : null,
                    receivedTokens: [],
                    sentTokens: [],
                    solTransfer,
                    sourceWallet: this.walletAddress,
                    destinationWallet: null
                };
            }
            return null;
        }

        // Separate wallet transfers from all transfers
        const walletReceivedTokens = tokenTransfers.filter(t => t.type === 'received' && t.isOwnerWallet);
        const walletSentTokens = tokenTransfers.filter(t => t.type === 'sent' && t.isOwnerWallet);
        const allReceivedTokens = tokenTransfers.filter(t => t.type === 'received');
        const allSentTokens = tokenTransfers.filter(t => t.type === 'sent');

        const hasSolReceived = solTransfer && solTransfer.type === 'received' && Math.abs(solTransfer.amount) > 0.000001;
        const hasSolSent = solTransfer && solTransfer.type === 'sent' && Math.abs(solTransfer.amount) > 0.000001;

        let tradeType = null;
        let buyTokenMint = null;
        let sellTokenMint = null;
        let destinationWallet = null;

        // Primary detection: Direct wallet token transfers
        if (walletReceivedTokens.length > 0 && hasSolSent) {
            // BUY: Wallet received tokens, sent SOL
            tradeType = 'BUY';
            buyTokenMint = walletReceivedTokens[0].mint;
            sellTokenMint = 'So11111111111111111111111111111111111111112'; // SOL
            // Find who sent us the tokens (destination wallet for our perspective)
            const tokenSender = allSentTokens.find(t => t.mint === buyTokenMint && !t.isOwnerWallet);
            destinationWallet = tokenSender ? tokenSender.owner : null;
        } else if (walletSentTokens.length > 0 && hasSolReceived) {
            // SELL: Wallet sent tokens, received SOL
            tradeType = 'SELL';
            buyTokenMint = 'So11111111111111111111111111111111111111112'; // SOL
            sellTokenMint = walletSentTokens[0].mint;
            // Find who received our tokens (destination wallet)
            const tokenReceiver = allReceivedTokens.find(t => t.mint === sellTokenMint && !t.isOwnerWallet);
            destinationWallet = tokenReceiver ? tokenReceiver.owner : null;
        } else if (walletReceivedTokens.length > 0 && walletSentTokens.length > 0) {
            // SWAP: Token to Token (wallet level) - make sure they're different tokens
            if (walletReceivedTokens[0].mint !== walletSentTokens[0].mint) {
                tradeType = 'SWAP';
                buyTokenMint = walletReceivedTokens[0].mint;
                sellTokenMint = walletSentTokens[0].mint;
                // For swaps, find the counterparty
                const tokenReceiver = allReceivedTokens.find(t => t.mint === sellTokenMint && !t.isOwnerWallet);
                destinationWallet = tokenReceiver ? tokenReceiver.owner : null;
            }
        }
        // Secondary detection: Look at all token movements in transaction
        else if (allReceivedTokens.length > 0 && hasSolSent) {
            // Potential BUY: Any account received tokens, wallet sent SOL
            tradeType = 'BUY';
            buyTokenMint = allReceivedTokens[0].mint;
            sellTokenMint = 'So11111111111111111111111111111111111111112';
            destinationWallet = allReceivedTokens[0].owner;
        } else if (allSentTokens.length > 0 && hasSolReceived) {
            // Potential SELL: Any account sent tokens, wallet received SOL
            tradeType = 'SELL';
            buyTokenMint = 'So11111111111111111111111111111111111111112';
            sellTokenMint = allSentTokens[0].mint;
            destinationWallet = allSentTokens[0].owner;
        } else if (allReceivedTokens.length > 0 && allSentTokens.length > 0) {
            // Potential SWAP: Token movements detected - make sure they're different tokens
            if (allReceivedTokens[0].mint !== allSentTokens[0].mint) {
                tradeType = 'SWAP';
                buyTokenMint = allReceivedTokens[0].mint;
                sellTokenMint = allSentTokens[0].mint;
                // For indirect swaps, try to find the counterparty
                destinationWallet = allReceivedTokens.find(t => !t.isOwnerWallet)?.owner ||
                                  allSentTokens.find(t => !t.isOwnerWallet)?.owner;
            }
        }

        // Additional validation: Don't allow SOL-to-SOL trades
        if (buyTokenMint === sellTokenMint && buyTokenMint === 'So11111111111111111111111111111111111111112') {
            return null; // Invalid trade - both tokens are SOL
        }

        return {
            tradeType,
            buyTokenMint,
            sellTokenMint,
            sourceWallet: this.walletAddress,
            destinationWallet,
            receivedTokens: walletReceivedTokens,
            sentTokens: walletSentTokens,
            allReceivedTokens,
            allSentTokens,
            solTransfer
        };
    }

    async displayTradeDetails(transaction, index) {
        const timestamp = transaction.blockTime ?
            new Date(transaction.blockTime * 1000).toLocaleString() : 'Unknown';

        const tradeDetails = this.analyzeTradeDetails(transaction.tokenTransfers, transaction.solTransfer);

        console.log(`\n📋 Transaction ${index + 1}`);
        console.log(`   🔗 Signature: ${transaction.signature}`);
        console.log(`   ⏰ Time: ${timestamp}`);
        console.log(`   ✅ Status: ${transaction.success ? 'Success' : 'Failed'}`);



        // Show signer information
        if (transaction.signer) {
            console.log(`   ✍️  Signer: ${transaction.signer}`);
            if (transaction.signer === this.walletAddress) {
                console.log(`   🔑 Signer Status: This is YOUR wallet (you signed this transaction)`);
            } else {
                console.log(`   🔑 Signer Status: External wallet signed this transaction`);
            }
        } else {
            console.log(`   ✍️  Signer: Unknown`);
        }

        if (tradeDetails && tradeDetails.tradeType) {
            const tradeEmoji = {
                'BUY': '🟢',
                'SELL': '🔴',
                'SWAP': '🔄',
                'SOL_RECEIVE': '💰',
                'SOL_SEND': '💸'
            };

            console.log(`   ${tradeEmoji[tradeDetails.tradeType] || '💫'} Trade Type: ${tradeDetails.tradeType}`);

            // Show wallet information
            console.log(`   👤 Source Wallet: ${tradeDetails.sourceWallet}`);
            if (tradeDetails.destinationWallet) {
                console.log(`   🎯 Destination Wallet: ${tradeDetails.destinationWallet}`);
            } else {
                console.log(`   🎯 Destination Wallet: Unknown (complex transaction)`);
            }

            // Get token metadata for better display
            if (tradeDetails.buyTokenMint) {
                const buyTokenInfo = await this.getTokenMetadata(tradeDetails.buyTokenMint);
                console.log(`   📥 Buy Token: ${buyTokenInfo.symbol} (${buyTokenInfo.name})`);
                console.log(`   📥 Buy Token Mint: ${tradeDetails.buyTokenMint}`);
            }

            if (tradeDetails.sellTokenMint) {
                const sellTokenInfo = await this.getTokenMetadata(tradeDetails.sellTokenMint);
                console.log(`   📤 Sell Token: ${sellTokenInfo.symbol} (${sellTokenInfo.name})`);
                console.log(`   📤 Sell Token Mint: ${tradeDetails.sellTokenMint}`);
            }

            // Show if detection was based on indirect token movements
            if (tradeDetails.allReceivedTokens && tradeDetails.allReceivedTokens.length > tradeDetails.receivedTokens.length) {
                console.log(`   🔍 Note: Trade detected from indirect token movements`);
            }

            // Show token amounts with metadata
            if (tradeDetails.receivedTokens.length > 0) {
                console.log(`   📈 Received:`);
                for (const token of tradeDetails.receivedTokens) {
                    const tokenInfo = await this.getTokenMetadata(token.mint);
                    console.log(`      ${token.amount.toFixed(6)} ${tokenInfo.symbol} (${tokenInfo.name})`);
                }
            }

            if (tradeDetails.sentTokens.length > 0) {
                console.log(`   📉 Sent:`);
                for (const token of tradeDetails.sentTokens) {
                    const tokenInfo = await this.getTokenMetadata(token.mint);
                    console.log(`      ${Math.abs(token.amount).toFixed(6)} ${tokenInfo.symbol} (${tokenInfo.name})`);
                }
            }

            if (tradeDetails.solTransfer) {
                const sol = tradeDetails.solTransfer;
                console.log(`   💎 SOL ${sol.type.toUpperCase()}: ${sol.amount.toFixed(6)} SOL`);
            }
        }
    }

    async run(targetCount = 30) {
        const overallStartTime = Date.now();
        try {
            console.log(`🔍 Fetching latest ${targetCount} signed transactions (OPTIMIZED)...`);
            const signedTrades = await this.getLatestSignedTransactions(targetCount);

            console.log('\n' + '='.repeat(80));
            console.log(`📊 YOUR LATEST ${targetCount} SIGNED TRADES (OPTIMIZED)`);
            console.log('='.repeat(80));

            if (signedTrades.length === 0) {
                console.log('📭 No signed trades found in recent wallet history.');
                console.log('💡 This might mean:');
                console.log('   - You haven\'t made any trades recently');
                console.log('   - Your trades are controlled by external wallets/bots');
                console.log('   - The wallet is used for receiving only');
                return;
            }

            // Calculate trade statistics
            const tradeStats = { BUY: 0, SELL: 0, SWAP: 0 };
            for (const transaction of signedTrades) {
                const tradeDetails = this.analyzeTradeDetails(transaction.tokenTransfers, transaction.solTransfer);
                if (tradeDetails && tradeDetails.tradeType) {
                    tradeStats[tradeDetails.tradeType]++;
                }
            }

            console.log(`🎯 Found ${signedTrades.length} signed trades:`);
            console.log(`   🟢 BUY: ${tradeStats.BUY} trades`);
            console.log(`   🔴 SELL: ${tradeStats.SELL} trades`);
            console.log(`   🔄 SWAP: ${tradeStats.SWAP} trades\n`);

            for (let i = 0; i < signedTrades.length; i++) {
                await this.displayTradeDetails(signedTrades[i], i);
            }

            const overallEndTime = Date.now();
            const overallTime = overallEndTime - overallStartTime;
            const overallTimeInSeconds = (overallTime / 1000).toFixed(2);

            console.log('\n' + '='.repeat(80));
            console.log(`✅ ${signedTrades.length} YOUR SIGNED trades displayed!`);
            console.log(`📊 Your Trade Summary: ${tradeStats.BUY} BUY | ${tradeStats.SELL} SELL | ${tradeStats.SWAP} SWAP`);
            console.log(`⚡ Optimized: Only processed transactions until ${targetCount} signed trades were found!`);
            console.log(`⏱️  TOTAL EXECUTION TIME: ${overallTimeInSeconds} seconds`);

        } catch (error) {
            console.error('❌ Error:', error.message);
            process.exit(1);
        }
    }

    // Keep the old method for complete history analysis if needed
    async runCompleteHistory() {
        const overallStartTime = Date.now();
        try {
            console.log('🔍 Fetching ALL transactions from wallet history...');
            console.log(`⏱️  Start time: ${new Date(overallStartTime).toLocaleTimeString()}`);
            const transactions = await this.getAllTransactions(); // Get ALL transactions

            console.log('\n' + '='.repeat(80));
            console.log('📊 YOUR SIGNED TRADES ONLY - COMPLETE WALLET HISTORY');
            console.log('='.repeat(80));

            if (transactions.length === 0) {
                console.log('📭 No transactions found in wallet history.');
                return;
            }

            // Filter to show only actual trades where YOUR wallet is the signer
            const actualTrades = [];
            const tradeStats = { BUY: 0, SELL: 0, SWAP: 0 };
            let totalTrades = 0;
            let yourSignedTrades = 0;

            for (const transaction of transactions) {
                const tradeDetails = this.analyzeTradeDetails(transaction.tokenTransfers, transaction.solTransfer);
                if (tradeDetails && tradeDetails.tradeType &&
                    ['BUY', 'SELL', 'SWAP'].includes(tradeDetails.tradeType)) {
                    totalTrades++;

                    // Only include trades where YOUR wallet is the signer
                    if (transaction.signer === this.walletAddress) {
                        actualTrades.push(transaction);
                        tradeStats[tradeDetails.tradeType]++;
                        yourSignedTrades++;
                    }
                }
            }

            if (actualTrades.length === 0) {
                console.log(`📭 No trades found where YOU are the signer in COMPLETE wallet history.`);
                console.log(`📊 Total transactions analyzed: ${transactions.length}`);
                console.log(`📊 Total trades found: ${totalTrades}`);
                console.log(`✍️  Your signed trades: ${yourSignedTrades}`);
                console.log(`🤖 External signed trades: ${totalTrades - yourSignedTrades}`);
                console.log('💡 This wallet appears to be completely controlled by external wallets/bots/smart contracts.');
                return;
            }

            console.log(`🎯 Found ${actualTrades.length} trades where YOU are the signer out of ${transactions.length} total transactions:`);
            console.log(`📊 Total transactions analyzed: ${transactions.length}`);
            console.log(`📊 Total trades found: ${totalTrades}`);
            console.log(`✍️  Your signed trades: ${yourSignedTrades}`);
            console.log(`🤖 External signed trades: ${totalTrades - yourSignedTrades}`);
            console.log(`   🟢 BUY: ${tradeStats.BUY} trades`);
            console.log(`   🔴 SELL: ${tradeStats.SELL} trades`);
            console.log(`   🔄 SWAP: ${tradeStats.SWAP} trades\n`);

            for (let i = 0; i < actualTrades.length; i++) {
                await this.displayTradeDetails(actualTrades[i], i);
            }

            const overallEndTime = Date.now();
            const overallTime = overallEndTime - overallStartTime;
            const overallTimeInSeconds = (overallTime / 1000).toFixed(2);

            console.log('\n' + '='.repeat(80));
            console.log(`✅ ${actualTrades.length} YOUR SIGNED trades displayed!`);
            console.log(`📊 Your Trade Summary: ${tradeStats.BUY} BUY | ${tradeStats.SELL} SELL | ${tradeStats.SWAP} SWAP`);
            console.log(`🤖 External trades filtered out: ${totalTrades - yourSignedTrades}`);
            console.log(`⏱️  TOTAL EXECUTION TIME: ${overallTimeInSeconds} seconds`);

        } catch (error) {
            console.error('❌ Error:', error.message);
            process.exit(1);
        }
    }
}

// Run the application if called directly
if (require.main === module) {
    const analyzer = new OptimizedWalletTradeAnalyzer();

    // Check command line arguments for target count
    const args = process.argv.slice(2);
    let targetCount = 30; // Default to 30 signed transactions

    // Show help if requested
    if (args.includes('--help') || args.includes('-h')) {
        console.log(`
🚀 OPTIMIZED WALLET TRANSACTION ANALYZER
========================================

USAGE:
  node wallet_transaction.js [OPTIONS]

OPTIONS:
  --count, -c <number>    Number of signed transactions to fetch (default: 30)
  --all, -a              Analyze complete wallet history (slower)
  --help, -h              Show this help message

EXAMPLES:
  node wallet_transaction.js                    # Get latest 30 signed transactions (FAST)
  node wallet_transaction.js --count 10         # Get latest 10 signed transactions
  node wallet_transaction.js -c 50              # Get latest 50 signed transactions
  node wallet_transaction.js --all              # Analyze complete history (SLOW)

OPTIMIZATIONS:
  ⚡ Early termination: Stops when target count is reached
  ⚡ Sequential processing: Reduces rate limiting
  ⚡ Smart filtering: Only processes signed transactions
  ⚡ Efficient batching: Uses smaller batches for better performance

PERFORMANCE:
  🟢 FAST: --count <number> (recommended for recent activity)
  🟡 SLOW: --all (complete history analysis)
        `);
        process.exit(0);
    }

    // Check for --count or -c argument
    const countIndex = args.findIndex(arg => arg === '--count' || arg === '-c');
    if (countIndex !== -1 && args[countIndex + 1]) {
        const parsedCount = parseInt(args[countIndex + 1]);
        if (!isNaN(parsedCount) && parsedCount > 0) {
            targetCount = parsedCount;
        }
    }

    // Check for --all flag to run complete history
    if (args.includes('--all') || args.includes('-a')) {
        console.log('🔍 Running complete history analysis...');
        analyzer.runCompleteHistory();
    } else {
        console.log(`🚀 Running optimized analysis for latest ${targetCount} signed transactions...`);
        analyzer.run(targetCount);
    }
}

module.exports = OptimizedWalletTradeAnalyzer;